inboundCall:
  name: 4 - Library Pop Script
  description: 4 - Library Pop Script
  division: Home
  startUpRef: "/inboundCall/tasks/task[Welcome_163]"
  defaultLanguage: en-au
  supportedLanguages:
    en-au:
      defaultLanguageSkill:
        noValue: true
      textToSpeech:
        Genesys TTS:
          voice: Kandyce
  variables:
    - stringVariable:
        name: Flow.AccountEnabled
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.AccountType
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.CallAniCallbackIsAcceptable
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.Campus
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.CampusSubstring
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.Courses
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.CoursesString
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.DisplayName
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.Email
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.ExtensionAttribute1
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.ExtensionAttribute2
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.ExtensionAttribute3
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.FlowLog
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.HasITAccount
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.Id
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.IdentifiedBy
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.IsCallbackNumberAcceptable
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.IsExecutive
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.IsTechnicalGroup
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.IsValidDeakinObject
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.IsVIP
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.LockReason
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.LockReasonString
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.PasswordExpiresInDays
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.PhoneAdditional
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.PhoneMobile
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.PhoneMobileCallbackIsAcceptable
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.PhoneNumber
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.PhoneNumberCallbackIsAcceptable
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.PhoneType
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.ProductionState
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.RoomSubstring
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.ScriptSet
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.ServiceNowSysId
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.SipAddress
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.Username
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.VNCCampus
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.VNCRoom
        initialValue:
          lit: ""
  settingsActionDefaults:
    playAudioOnSilence:
      timeout:
        lit:
          seconds: 40
    detectSilence:
      timeout:
        lit:
          seconds: 40
    callData:
      processingPrompt:
        noValue: true
    callBridge:
      processingPrompt:
        noValue: true
    collectInput:
      noEntryTimeout:
        lit:
          seconds: 10
    dialByExtension:
      interDigitTimeout:
        lit:
          seconds: 6
    transferToUser:
      connectTimeout:
        noValue: true
    transferToNumber:
      connectTimeout:
        noValue: true
    transferToGroup:
      connectTimeout:
        noValue: true
    transferToFlowSecure:
      connectTimeout:
        lit:
          seconds: 15
  settingsErrorHandling:
    errorHandling:
      task:
        targetTaskRef: "/inboundCall/tasks/task[Error Handling_196]"
  settingsMenu:
    extensionDialingMaxDelay:
      lit:
        seconds: 1
    listenForExtensionDialing:
      lit: false
    menuSelectionTimeout:
      lit:
        seconds: 10
    repeatCount:
      lit: 3
  settingsPrompts:
    ensureAudioInPrompts: false
  settingsSpeechRec:
    completeMatchTimeout:
      lit:
        ms: 500
    incompleteMatchTimeout:
      lit:
        ms: 1500
    maxSpeechLengthTimeout:
      lit:
        seconds: 20
    minConfidenceLevel:
      lit: 50
    asrCompanyDir: startUpObject
    asrEnabledOnFlow: false
  tasks:
    - task:
        name: Pop Script
        refId: Pop Script_14
        actions:
          - updateData:
              name: Update Flow log
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: Append(Flow.FlowLog, "Setting pop script.\n")
          - setScreenPop:
              name: Set Screen Pop
              screenPopScript:
                LibraryScript:
                  inputs:
                    Campus:
                      exp: Flow.Campus
                    LockReason:
                      exp: Flow.LockReasonString
                    accountIsLocked:
                      exp: Flow.LockReasonString != "No Lock"
                    SNContactType:
                      noValue: true
                    PhoneNumber:
                      exp: Flow.PhoneNumber
                    IsValidDeakinObject:
                      exp: Flow.IsValidDeakinObject
                    Inv_IsValidDeakinObject:
                      exp: "!ToBool(Flow.IsValidDeakinObject)"
                    isStaff:
                      exp: Flow.AccountType == "STAFF"
                    CallBackDate:
                      lit: ""
                    SNSys_ID:
                      exp: Flow.ServiceNowSysId
                    showTickets:
                      lit: false
                    CallAni:
                      exp: Call.Ani
                    ScriptPanelColour:
                      lit: rgb(229, 239, 241)
                    containsMoreInfo:
                      lit: false
                    CustomerDisplayName:
                      exp: Flow.DisplayName
                    Username:
                      exp: Flow.Username
                    Id:
                      exp: Flow.Id
                    PhoneMobile:
                      exp: Flow.PhoneMobile
                    Email:
                      exp: Flow.Email
                    InScriptBridgeRan:
                      lit: false
                    customerFound:
                      exp: Flow.AccountType == "STUDENT" or Flow.AccountType == "STAFF"
                    passwordHasExpired:
                      exp: ToInt(Flow.PasswordExpiresInDays) <= 0
                    passwordCannotExpire:
                      exp: ToInt(Flow.PasswordExpiresInDays) >= 32767
                    Course:
                      exp: Flow.CoursesString
                    isStudent:
                      exp: Flow.AccountType == "STUDENT"
                    PhoneType:
                      exp: Flow.PhoneType
                    accountHasIssue:
                      exp: ToInt(Flow.PasswordExpiresInDays) <= 0 or Flow.LockReasonString != "No Lock"
                    isCallBack:
                      lit: false
                    RequestedCallbackNumber:
                      lit: ""
                    InteractionID:
                      exp: Interaction.Id
                    ConversationID:
                      exp: Call.ConversationId
                    IdentifiedBy:
                      exp: "If(Flow.IdentifiedBy == \"phone\", \"Identified by: Phone Number .\", If(Flow.IdentifiedBy == \"id\", \"Identified by: ID Number .\", \"\"))"
                    Faculty:
                      lit: ""
                    ServiceNowButtonHidden:
                      lit: true
                    AccountType:
                      exp: Flow.AccountType
                    ScriptSetRequiredSkill:
                      lit: ""
                    ServiceNow_Env:
                      lit: deakin.service-now.com
          - updateData:
              name: Update Data
              statements:
                - string:
                    variable: Flow.ScriptSet
                    value:
                      lit: Library
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.ScriptSet
                    value:
                      exp: Flow.ScriptSet
          - setParticipantData:
              name: Set Flow log
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - transferToFlow:
              name: Transfer to Flow
              targetFlow:
                name: 5 - Library
              failureOutputs:
                errorType:
                  noValue: true
                errorMessage:
                  noValue: true
              outputs:
                failure:
                  actions:
                    - disconnect:
                        name: Disconnect
    - task:
        name: Welcome
        refId: Welcome_163
        variables:
          - integerVariable:
              name: Task.DistanceFromFirstToLastIndex
              initialValue:
                lit: 0
          - integerVariable:
              name: Task.EndOfLocationIndex
              initialValue:
                lit: 0
          - integerVariable:
              name: Task.FirstHyphenIndex
              initialValue:
                lit: 0
          - integerVariable:
              name: Task.FirstSpaceIndex
              initialValue:
                lit: 0
          - stringVariable:
              name: Task.Location
              initialValue:
                lit: ""
          - integerVariable:
              name: Task.SecondHyphenIndex
              initialValue:
                lit: 0
        actions:
          - getParticipantData:
              name: Get Flow.ProductionState
              attributes:
                - attribute:
                    name:
                      lit: Flow.ProductionState
                    variable: Flow.ProductionState
          - getParticipantData:
              name: Get Bridge data
              attributes:
                - attribute:
                    name:
                      lit: Flow.IsExecutive
                    variable: Flow.IsExecutive
                - attribute:
                    name:
                      lit: Flow.Id
                    variable: Flow.Id
                - attribute:
                    name:
                      lit: Flow.Username
                    variable: Flow.Username
                - attribute:
                    name:
                      lit: Flow.DisplayName
                    variable: Flow.DisplayName
                - attribute:
                    name:
                      lit: Flow.AccountType
                    variable: Flow.AccountType
                - attribute:
                    name:
                      lit: Flow.Campus
                    variable: Flow.Campus
                - attribute:
                    name:
                      lit: Flow.PhoneNumber
                    variable: Flow.PhoneNumber
                - attribute:
                    name:
                      lit: Flow.PhoneMobile
                    variable: Flow.PhoneMobile
                - attribute:
                    name:
                      lit: Flow.PhoneAdditional
                    variable: Flow.PhoneAdditional
                - attribute:
                    name:
                      lit: Flow.Email
                    variable: Flow.Email
                - attribute:
                    name:
                      lit: Flow.SipAddress
                    variable: Flow.SipAddress
                - attribute:
                    name:
                      lit: Flow.AccountEnabled
                    variable: Flow.AccountEnabled
                - attribute:
                    name:
                      lit: Flow.LockReason
                    variable: Flow.LockReason
                - attribute:
                    name:
                      lit: Flow.PasswordExpiresInDays
                    variable: Flow.PasswordExpiresInDays
                - attribute:
                    name:
                      lit: Flow.Courses
                    variable: Flow.Courses
                - attribute:
                    name:
                      lit: Flow.PhoneType
                    variable: Flow.PhoneType
                - attribute:
                    name:
                      lit: Flow.ServiceNowSysId
                    variable: Flow.ServiceNowSysId
                - attribute:
                    name:
                      lit: Flow.IsValidDeakinObject
                    variable: Flow.IsValidDeakinObject
                - attribute:
                    name:
                      lit: Flow.IsTechnicalGroup
                    variable: Flow.IsTechnicalGroup
                - attribute:
                    name:
                      lit: Flow.IsVIP
                    variable: Flow.IsVIP
                - attribute:
                    name:
                      lit: Flow.HasITAccount
                    variable: Flow.HasITAccount
                - attribute:
                    name:
                      lit: Flow.ExtensionAttribute1
                    variable: Flow.ExtensionAttribute1
                - attribute:
                    name:
                      lit: Flow.ExtensionAttribute2
                    variable: Flow.ExtensionAttribute2
                - attribute:
                    name:
                      lit: Flow.ExtensionAttribute3
                    variable: Flow.ExtensionAttribute3
          - getParticipantData:
              name: Get Other data
              attributes:
                - attribute:
                    name:
                      lit: Flow.PhoneNumberCallbackIsAcceptable
                    variable: Flow.PhoneNumberCallbackIsAcceptable
                - attribute:
                    name:
                      lit: Flow.PhoneMobileCallbackIsAcceptable
                    variable: Flow.PhoneMobileCallbackIsAcceptable
                - attribute:
                    name:
                      lit: Flow.IsCallbackNumberAcceptable
                    variable: Flow.IsCallbackNumberAcceptable
                - attribute:
                    name:
                      lit: Flow.CoursesString
                    variable: Flow.CoursesString
                - attribute:
                    name:
                      lit: Flow.CallAniCallbackIsAcceptable
                    variable: Flow.CallAniCallbackIsAcceptable
                - attribute:
                    name:
                      lit: Flow.LockReasonString
                    variable: Flow.LockReasonString
                - attribute:
                    name:
                      lit: Flow.IdentifiedBy
                    variable: Flow.IdentifiedBy
          - getParticipantData:
              name: Get Flow log
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    variable: Flow.FlowLog
          - updateData:
              name: Update Data
              statements:
                - string:
                    variable: Flow.CampusSubstring
                    value:
                      exp: "\"\""
                - string:
                    variable: Flow.RoomSubstring
                    value:
                      exp: "\"\""
                - string:
                    variable: Flow.VNCCampus
                    value:
                      exp: "\"\""
                - string:
                    variable: Flow.VNCRoom
                    value:
                      exp: "\"\""
          - decision:
              name: Flow.PhoneType == "TS" or Flow.PhoneType == "SHRD"
              condition:
                exp: Flow.PhoneType == "TS" or Flow.PhoneType == "SHRD"
              outputs:
                yes:
                  actions:
                    - updateData:
                        name: Split retrieved location
                        statements:
                          - string:
                              variable: Task.Location
                              value:
                                exp: Flow.DisplayName
                          - integer:
                              variable: Task.FirstHyphenIndex
                              value:
                                exp: FindString(Task.Location,"-",1)
                          - integer:
                              variable: Task.SecondHyphenIndex
                              value:
                                exp: FindString(Task.Location, "-", 2)
                          - integer:
                              variable: Task.FirstSpaceIndex
                              value:
                                exp: FindString(Task.Location, " ", 1)
                          - integer:
                              variable: Task.EndOfLocationIndex
                              value:
                                exp: If(Task.SecondHyphenIndex != -1, Task.SecondHyphenIndex, If(Task.FirstSpaceIndex != -1, Task.FirstSpaceIndex, -1))
                          - integer:
                              variable: Task.DistanceFromFirstToLastIndex
                              value:
                                exp: If(Task.EndOfLocationIndex != -1, (Task.EndOfLocationIndex - (Task.FirstHyphenIndex + 1)) ,0)
                          - string:
                              variable: Flow.CampusSubstring
                              value:
                                exp: If(Task.FirstHyphenIndex == 1, Substring(Task.Location, 0, 1), "")
                          - string:
                              variable: Flow.RoomSubstring
                              value:
                                exp: "If(Task.FirstHyphenIndex == 1, Substring(Task.Location, Task.FirstHyphenIndex + 1, Task.DistanceFromFirstToLastIndex), \"\")\n"
          - jumpToTask:
              name: Jump to Reusable Task
              targetTaskRef: "/inboundCall/tasks/task[Pop Script_14]"
    - task:
        name: Error Handling
        refId: Error Handling_196
        actions:
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - transferToFlow:
              name: Transfer to Flow
              targetFlow:
                name: Error Handling Flow
              failureOutputs:
                errorType:
                  noValue: true
                errorMessage:
                  noValue: true
          - disconnect:
              name: Disconnect

inboundCall:
  name: 1 - Phone Bridge and Welcome
  description: "(PCC: acg-purecloud-admin) "
  division: Home
  startUpRef: "/inboundCall/tasks/task[Welcome Task_13]"
  defaultLanguage: en-au
  supportedLanguages:
    en-au:
      defaultLanguageSkill:
        noValue: true
      textToSpeech:
        defaultEngine:
          voice: Kandy<PERSON>
  variables:
    - booleanVariable:
        name: Flow.AccountEnabled
        initialValue:
          noValue: true
    - stringCollectionVariable:
        name: Flow.AccountNotes
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.AccountType
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.AniDiallingCode
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.CallAni
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.CalledAddressOriginal
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.CallFromInternational
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.Campus
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.ConversationID
        initialValue:
          noValue: true
    - stringCollectionVariable:
        name: Flow.Courses
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.CoursesString
        initialValue:
          lit: ""
    - integerVariable:
        name: Flow.DayOfWeek
        initialValue:
          lit: 0
    - stringVariable:
        name: Flow.DisplayName
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.DSLDirectRoutingRequired
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.Email
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.ErrorStage
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.ExtensionAttribute1
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.ExtensionAttribute2
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.ExtensionAttribute3
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.Faculties
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.Faculty
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.FlowLog
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.FlowLog2
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.HasITAccount
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.Id
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.IdentifiedBy
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.IsCloudStudent
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.IsDev
        initialValue:
          noValue: true
    - durationVariable:
        name: Flow.IsDevDuration
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.IsDevInv
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.IsExecutive
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.IsTechnicalGroup
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.IsValidDeakinObject
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.IsVIP
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.IsWeekday
        initialValue:
          lit: false
    - stringVariable:
        name: Flow.IvrName
        initialValue:
          lit: ""
    - stringCollectionVariable:
        name: Flow.LockReason
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.LockReasonString
        initialValue:
          lit: ""
    - stringCollectionVariable:
        name: Flow.MemberOfGroups
        initialValue:
          noValue: true
    - integerVariable:
        name: Flow.OverridePriority
        initialValue:
          noValue: true
    - integerVariable:
        name: Flow.PasswordExpiresInDays
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.PCC_Duo_SD_Auth_On
        initialValue:
          noValue: true
    - stringCollectionVariable:
        name: Flow.PhoneAdditional
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.PhoneBridgeGroupsLoopRun
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.PhoneBridgeGroupsLoopRun1
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.PhoneMobile
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.PhoneNumber
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.PhoneType
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.ReportBridgeEvent
        initialValue:
          noValue: true
    - skillVariable:
        name: Flow.RequiredSkill
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.Schools
        initialValue:
          noValue: true
    - stringCollectionVariable:
        name: Flow.SchoolsCollection
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.SchoolsInFacultyString
        initialValue:
          noValue: true
    - integerVariable:
        name: Flow.SchoolsInt
        initialValue:
          noValue: true
    - integerVariable:
        name: Flow.SchoolsLoopCount
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.ScriptSetRequiredSkill
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.ServiceNowSysId
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.SipAddress
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.TestingNumber
        initialValue:
          noValue: true
    - integerVariable:
        name: Flow.TimeOfDay
        initialValue:
          lit: 0
    - stringVariable:
        name: Flow.Username
        initialValue:
          noValue: true
    - integerVariable:
        name: Flow.UTCOffsetMinutes
        initialValue:
          noValue: true
  settingsActionDefaults:
    playAudioOnSilence:
      timeout:
        lit:
          seconds: 40
    detectSilence:
      timeout:
        lit:
          seconds: 40
    callData:
      processingPrompt:
        noValue: true
    collectInput:
      noEntryTimeout:
        lit:
          seconds: 10
    dialByExtension:
      interDigitTimeout:
        lit:
          seconds: 6
    transferToUser:
      connectTimeout:
        noValue: true
    transferToNumber:
      connectTimeout:
        noValue: true
    transferToGroup:
      connectTimeout:
        noValue: true
    transferToFlowSecure:
      connectTimeout:
        lit:
          seconds: 15
  settingsErrorHandling:
    errorHandling:
      task:
        targetTaskRef: "/inboundCall/tasks/task[Error Handling_10]"
    preHandlingAudio:
      tts: ""
  settingsMenu:
    extensionDialingMaxDelay:
      lit:
        seconds: 1
    listenForExtensionDialing:
      lit: false
    menuSelectionTimeout:
      lit:
        seconds: 10
    repeatCount:
      lit: 3
  settingsPrompts:
    ensureAudioInPrompts: false
    promptMediaToValidate:
      - mediaType: audio
      - mediaType: tts
  settingsSpeechRec:
    completeMatchTimeout:
      lit:
        ms: 500
    incompleteMatchTimeout:
      lit:
        ms: 1500
    maxSpeechLengthTimeout:
      lit:
        seconds: 20
    minConfidenceLevel:
      lit: 50
    asrCompanyDir: startUpObject
    asrEnabledOnFlow: false
    suppressRecording: true
  tasks:
    - task:
        name: Error Handling
        refId: Error Handling_10
        actions:
          - setParticipantData:
              name: Set Flow log
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - transferToFlow:
              name: Transfer to Flow
              targetFlow:
                name: Error Handling Flow
              failureOutputs:
                errorType:
                  noValue: true
                errorMessage:
                  noValue: true
          - disconnect:
              name: Disconnect
    - task:
        name: Groups Loop
        refId: Groups Loop_11
        variables:
          - integerVariable:
              name: Task.FacultyGroupIndex
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - integerVariable:
              name: Task.SchoolsGroupIndex
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
        actions:
          - updateData:
              name: Update Data
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: Append(Flow.FlowLog, "Starting groups loop in phone bridge", "\n")
                - string:
                    variable: Flow.Faculties
                    value:
                      lit: ""
                - string:
                    variable: Flow.Schools
                    value:
                      lit: ""
                - string:
                    variable: Flow.ScriptSetRequiredSkill
                    value:
                      lit: ""
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - switch:
              name: Flow.AccountType=="STUDENT"
              evaluate:
                firstTrue:
                  cases:
                    - case:
                        value:
                          exp: Flow.AccountType=="STUDENT"
                        actions:
                          - updateData:
                              name: Update Data
                              statements:
                                - integer:
                                    variable: Task.FacultyGroupIndex
                                    value:
                                      lit: 1
                                - integer:
                                    variable: Task.SchoolsGroupIndex
                                    value:
                                      lit: 0
                          - loop:
                              name: Loop
                              currentIndex:
                                var: Task.FacultyGroupIndex
                              loopCount:
                                exp: Count(Flow.MemberOfGroups)
                              outputs:
                                loop:
                                  actions:
                                    - decision:
                                        name: Decision
                                        condition:
                                          exp: Contains(GetAt(Flow.MemberOfGroups,Task.FacultyGroupIndex),"wgf-")
                                        outputs:
                                          "yes":
                                            actions:
                                              - updateData:
                                                  name: Update Data
                                                  statements:
                                                    - string:
                                                        variable: Flow.Faculties
                                                        value:
                                                          exp: "Append(\n  Flow.Faculties,\n  \"{\",\n  Substring(\n    GetAt(\n      Flow.MemberOfGroups,\n      Task.FacultyGroupIndex\n    ),\n    4,\n    FindString(\n      GetAt(\n        Flow.MemberOfGroups,\n        Task.FacultyGroupIndex\n      ),\n\t  \";\"\n    )-4\n  ),\n  \"}\"\n)"
                          - loop:
                              name: Loop
                              currentIndex:
                                var: Task.SchoolsGroupIndex
                              loopCount:
                                exp: Count(Flow.MemberOfGroups)
                              outputs:
                                loop:
                                  actions:
                                    - decision:
                                        name: Decision
                                        condition:
                                          exp: Contains(GetAt(Flow.MemberOfGroups,Task.SchoolsGroupIndex),"wgsch-")
                                        outputs:
                                          "yes":
                                            actions:
                                              - updateData:
                                                  name: Update Data
                                                  statements:
                                                    - string:
                                                        variable: Flow.Schools
                                                        value:
                                                          exp: "Append(\n  Flow.Schools,\n  Substring(\n    GetAt(\n      Flow.MemberOfGroups,\n      Task.SchoolsGroupIndex\n    ),\n    6,\n    FindString(\n      GetAt(\n        Flow.MemberOfGroups,\n        Task.SchoolsGroupIndex\n      ),\n\t  \";\"\n    )-6\n  ),\n  \",\"\n)"
                          - decision:
                              name: Decision
                              condition:
                                exp: IsNotSetOrEmpty(Flow.Schools)
                              outputs:
                                "yes":
                                  actions:
                                    - updateData:
                                        name: Update Data
                                        statements:
                                          - string:
                                              variable: Flow.Schools
                                              value:
                                                lit: No schools identified.
                                    - setParticipantData:
                                        name: Set Participant Data
                                        attributes:
                                          - attribute:
                                              name:
                                                lit: Flow.Faculties
                                              value:
                                                exp: Flow.Faculties
                                          - attribute:
                                              name:
                                                lit: Flow.IsCloudStudent
                                              value:
                                                exp: Flow.IsCloudStudent
                                          - attribute:
                                              name:
                                                lit: Flow.Schools
                                              value:
                                                exp: Flow.Schools
                                          - attribute:
                                              name:
                                                lit: Flow.RequiredSkill
                                              value:
                                                exp: Flow.RequiredSkill
                                          - attribute:
                                              name:
                                                lit: Flow.SchoolsInFacultyString
                                              value:
                                                lit: No schools identified from faculty
                                "no":
                                  actions:
                                    - updateData:
                                        name: Update Data
                                        statements:
                                          - string:
                                              variable: Flow.IsCloudStudent
                                              value:
                                                exp: FindFirst(Flow.MemberOfGroups,"wg-all-students-cloud;_ALL Students Cloud")!=-1
                                          - string:
                                              variable: Flow.FlowLog
                                              value:
                                                exp: Append(Flow.FlowLog, "groups logic completed in phone bridge ", Flow.Faculties ,"\n")
                                          - boolean:
                                              variable: Flow.PhoneBridgeGroupsLoopRun1
                                              value:
                                                lit: true
                                          - stringCollection:
                                              variable: Flow.SchoolsCollection
                                              value:
                                                exp: Split(Flow.Schools,",")
                                          - integer:
                                              variable: Flow.SchoolsInt
                                              value:
                                                lit: 0
                                          - string:
                                              variable: Flow.SchoolsInFacultyString
                                              value:
                                                lit: ""
                                    - loop:
                                        name: Loop
                                        currentIndex:
                                          var: Flow.SchoolsLoopCount
                                        loopCount:
                                          exp: Count(Flow.SchoolsCollection)
                                        outputs:
                                          loop:
                                            actions:
                                              - decision:
                                                  name: Decision
                                                  condition:
                                                    exp: FindString(GetAt(Flow.SchoolsCollection,Flow.SchoolsLoopCount),Substring(Flow.Faculties,1,2))==0
                                                  outputs:
                                                    "yes":
                                                      actions:
                                                        - updateData:
                                                            name: Update Data
                                                            statements:
                                                              - string:
                                                                  variable: Flow.SchoolsInFacultyString
                                                                  value:
                                                                    exp: "Append(\n  Flow.SchoolsInFacultyString,\n  \",\",\n    GetAt(\n      Flow.SchoolsCollection,\n      Flow.SchoolsLoopCount\n    )\n)"
                                    - setParticipantData:
                                        name: Set Participant Data
                                        attributes:
                                          - attribute:
                                              name:
                                                lit: Flow.Faculties
                                              value:
                                                exp: Flow.Faculties
                                          - attribute:
                                              name:
                                                lit: Flow.IsCloudStudent
                                              value:
                                                exp: Flow.IsCloudStudent
                                          - attribute:
                                              name:
                                                lit: Flow.Schools
                                              value:
                                                exp: Substring(Flow.Schools,0,Length(Flow.Schools)-1)
                                          - attribute:
                                              name:
                                                lit: Flow.RequiredSkill
                                              value:
                                                exp: Flow.RequiredSkill
                                          - attribute:
                                              name:
                                                lit: Flow.SchoolsInFacultyString
                                              value:
                                                exp: Substring(Flow.SchoolsInFacultyString,1,Length(Flow.SchoolsInFacultyString))
          - switch:
              name: Faculties switch
              evaluate:
                firstTrue:
                  default:
                    actions:
                      - decision:
                          name: Decision
                          condition:
                            exp: IsNotSetOrEmpty(Flow.Faculty)
                          outputs:
                            "yes":
                              actions:
                                - updateData:
                                    name: Update Data
                                    statements:
                                      - string:
                                          variable: Flow.Faculty
                                          value:
                                            lit: No Faculty Identified
                  cases:
                    - case:
                        value:
                          exp: Flow.Faculties=="{01}"
                        actions:
                          - updateData:
                              name: Update Data
                              statements:
                                - string:
                                    variable: Flow.Faculty
                                    value:
                                      lit: Faculty of Business and Law
                    - case:
                        value:
                          exp: Flow.Faculties=="{03}"
                        actions:
                          - updateData:
                              name: Update Data
                              statements:
                                - string:
                                    variable: Flow.Faculty
                                    value:
                                      lit: Faculty of Health
                    - case:
                        value:
                          exp: Flow.Faculties=="{04}"
                        actions:
                          - updateData:
                              name: Update Data
                              statements:
                                - string:
                                    variable: Flow.Faculty
                                    value:
                                      lit: Faculty of Arts and Education
                    - case:
                        value:
                          exp: Flow.Faculties=="{05}"
                        actions:
                          - updateData:
                              name: Update Data
                              statements:
                                - string:
                                    variable: Flow.Faculty
                                    value:
                                      lit: Faculty of Science, Engineering and Built Environment
                    - case:
                        value:
                          exp: Flow.Faculties=="{09}"
                    - case:
                        value:
                          exp: Flow.Faculties=="{10}"
                    - case:
                        value:
                          exp: Flow.Faculties=="{17}"
          - updateData:
              name: Update Data
              statements:
                - boolean:
                    variable: Flow.DSLDirectRoutingRequired
                    value:
                      exp: "(Flow.AccountType==\"Phone\" and (Contains(Flow.DisplayName,\"CCR\") or Contains(Flow.DisplayName,\"MCR\"))) or Flow.TestingNumber"
                - boolean:
                    variable: Flow.CallFromInternational
                    value:
                      exp: ToPhoneNumber(Call.Ani).dialingCode!=System.Regions.AU.dialingCode
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
                - attribute:
                    name:
                      lit: Flow.CallFromInternational
                    value:
                      exp: Flow.CallFromInternational
          - updateData:
              name: Update Data
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog, If(ToString(Flow.DSLDirectRoutingRequired)==\"true\",\"Flow.DSLDirectRoutingRequired = true\",\"\"), \"\\n\",\n\"Priority set is: \",ToString(Flow.OverridePriority), \"\\n\")"
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.PhoneBridgeGroupsLoopRun
                    value:
                      exp: Flow.PhoneBridgeGroupsLoopRun
                - attribute:
                    name:
                      lit: ScriptSetRequiredSkill
                    value:
                      exp: Flow.ScriptSetRequiredSkill
                - attribute:
                    name:
                      lit: Flow.Faculty
                    value:
                      exp: Flow.Faculty
                - attribute:
                    name:
                      lit: Flow.OverridePriority
                    value:
                      exp: Flow.OverridePriority
                - attribute:
                    name:
                      lit: Flow.DSLDirectRoutingRequired
                    value:
                      exp: Flow.DSLDirectRoutingRequired
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - jumpToTask:
              name: Jump to Reusable Task
              targetTaskRef: "/inboundCall/tasks/task[Route to MOTD_12]"
    - task:
        name: Route to MOTD
        refId: Route to MOTD_12
        variables:
          - integerVariable:
              name: Task.AccountLockArrayIndex
              initialValue:
                lit: 0
              isInput: false
              isOutput: false
          - integerVariable:
              name: Task.CoursesArrayIndex
              initialValue:
                lit: 0
              isInput: false
              isOutput: false
          - stringVariable:
              name: Task.NullPhoneNumber
              initialValue:
                lit: ""
              isInput: false
              isOutput: false
        actions:
          - updateData:
              name: Set variables
              statements:
                - string:
                    variable: Flow.CoursesString
                    value:
                      exp: "\"\""
                - string:
                    variable: Flow.LockReasonString
                    value:
                      exp: "\"\""
                - string:
                    variable: Task.NullPhoneNumber
                    value:
                      lit: "0"
                - string:
                    variable: Flow.ErrorStage
                    value:
                      lit: "\"\""
          - switch:
              name: Switch
              evaluate:
                firstTrue:
                  default:
                    actions:
                      - playAudio:
                          name: All Calls Recorded - Generic
                          audio:
                            exp: Prompt.AllCallsRecorded
                  cases:
                    - case:
                        value:
                          exp: Flow.IvrName=="DSA"
                        actions:
                          - playAudio:
                              name: All Calls Recorded - DSA
                              audio:
                                exp: AudioPlaybackOptions(Append(ToAudio(Prompt.SSNCallRecorded), ToAudioBlank(100)), false)
                    - case:
                        value:
                          exp: Flow.IvrName == "People Connect" or Flow.IvrName == "HR Help Line"
                        actions:
                          - playAudio:
                              name: Play Audio
                              audio:
                                exp: AudioPlaybackOptions(Append(ToAudioBlank(100), ToAudio(Prompt.TTSWereheretoassistyouNeural2B), ToAudioBlank(100)), true)
          - decision:
              name: Decision
              condition:
                exp: Flow.IsValidDeakinObject
              outputs:
                "yes":
                  actions:
                    - updateData:
                        name: Update Identification Method
                        statements:
                          - string:
                              variable: Flow.IdentifiedBy
                              value:
                                lit: phone
                    - updateData:
                        name: Update Flow log
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: Append(Flow.FlowLog,"Attempting to validate collections.\n")
                    - decision:
                        name: Are there courses?
                        condition:
                          exp: GetAt(Flow.Courses,0) != "No Value"
                        outputs:
                          "yes":
                            actions:
                              - loop:
                                  name: Loop
                                  currentIndex:
                                    var: Task.CoursesArrayIndex
                                  loopCount:
                                    exp: Count(Flow.Courses)
                                  outputs:
                                    loop:
                                      actions:
                                        - updateData:
                                            name: Update Data
                                            statements:
                                              - string:
                                                  variable: Flow.CoursesString
                                                  value:
                                                    exp: Append(Flow.CoursesString, If(Task.CoursesArrayIndex > 0, " / ",""), GetAt(Flow.Courses, Task.CoursesArrayIndex))
                          "no":
                            actions:
                              - updateData:
                                  name: Update Data
                                  statements:
                                    - string:
                                        variable: Flow.CoursesString
                                        value:
                                          lit: No Courses
                    - decision:
                        name: Is the account locked?
                        condition:
                          exp: GetAt(Flow.LockReason,0) != "No Value"
                        outputs:
                          "yes":
                            actions:
                              - loop:
                                  name: Loop
                                  currentIndex:
                                    var: Task.AccountLockArrayIndex
                                  loopCount:
                                    exp: Count(Flow.LockReason)
                                  outputs:
                                    loop:
                                      actions:
                                        - updateData:
                                            name: Update Data
                                            statements:
                                              - string:
                                                  variable: Flow.LockReasonString
                                                  value:
                                                    exp: Append(Flow.LockReasonString, " ",ToString(Task.AccountLockArrayIndex + 1), ". ", GetAt(Flow.LockReason, Task.AccountLockArrayIndex))
                          "no":
                            actions:
                              - updateData:
                                  name: Update Data
                                  statements:
                                    - string:
                                        variable: Flow.LockReasonString
                                        value:
                                          lit: No Lock
                    - updateData:
                        name: Update Flow log
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: Append(Flow.FlowLog,"Successfully validated collections.\n")
                    - setParticipantData:
                        name: Set Flow.FlowLog
                        attributes:
                          - attribute:
                              name:
                                lit: Flow.FlowLog
                              value:
                                exp: Flow.FlowLog
                "no":
                  actions:
                    - updateData:
                        name: Set collection strings
                        statements:
                          - string:
                              variable: Flow.LockReasonString
                              value:
                                exp: "\"No Lock\""
                          - string:
                              variable: Flow.CoursesString
                              value:
                                exp: "\"No Courses\""
          - updateData:
              name: Update Flow log
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: Append(Flow.FlowLog, "Attempting to set data in ID task.\n")
          - setParticipantData:
              name: Set Call Bridge Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.Id
                    value:
                      exp: Flow.Id
                - attribute:
                    name:
                      lit: Flow.Username
                    value:
                      exp: Flow.Username
                - attribute:
                    name:
                      lit: Flow.DisplayName
                    value:
                      exp: Flow.DisplayName
                - attribute:
                    name:
                      lit: Flow.AccountType
                    value:
                      exp: Flow.AccountType
                - attribute:
                    name:
                      lit: Flow.Campus
                    value:
                      exp: Flow.Campus
                - attribute:
                    name:
                      lit: Flow.PhoneNumber
                    value:
                      exp: Flow.PhoneNumber
                - attribute:
                    name:
                      lit: Flow.PhoneMobile
                    value:
                      exp: Flow.PhoneMobile
                - attribute:
                    name:
                      lit: Flow.PhoneAdditional
                    value:
                      exp: Flow.PhoneAdditional
                - attribute:
                    name:
                      lit: Flow.Email
                    value:
                      exp: Flow.Email
                - attribute:
                    name:
                      lit: Flow.SipAddress
                    value:
                      exp: Flow.SipAddress
                - attribute:
                    name:
                      lit: Flow.AccountEnabled
                    value:
                      exp: Flow.AccountEnabled
                - attribute:
                    name:
                      lit: Flow.LockReason
                    value:
                      exp: Flow.LockReason
                - attribute:
                    name:
                      lit: Flow.PasswordExpiresInDays
                    value:
                      exp: Flow.PasswordExpiresInDays
                - attribute:
                    name:
                      lit: Flow.Courses
                    value:
                      exp: Flow.Courses
                - attribute:
                    name:
                      lit: Flow.PhoneType
                    value:
                      exp: Flow.PhoneType
                - attribute:
                    name:
                      lit: Flow.ServiceNowSysId
                    value:
                      exp: Flow.ServiceNowSysId
                - attribute:
                    name:
                      lit: Flow.IsValidDeakinObject
                    value:
                      exp: Flow.IsValidDeakinObject
                - attribute:
                    name:
                      lit: Flow.IsVIP
                    value:
                      exp: Flow.IsVIP
                - attribute:
                    name:
                      lit: Flow.IsExecutive
                    value:
                      exp: Flow.IsExecutive
                - attribute:
                    name:
                      lit: Flow.IsTechnicalGroup
                    value:
                      exp: Flow.IsTechnicalGroup
                - attribute:
                    name:
                      lit: Flow.HasITAccount
                    value:
                      exp: Flow.HasITAccount
                - attribute:
                    name:
                      lit: Flow.TimeOfDay
                    value:
                      exp: Flow.TimeOfDay
                - attribute:
                    name:
                      lit: Flow.DayOfWeek
                    value:
                      exp: Flow.DayOfWeek
                - attribute:
                    name:
                      lit: Flow.UTCOffsetMinutes
                    value:
                      exp: Flow.UTCOffsetMinutes
                - attribute:
                    name:
                      lit: Flow.IsWeekday
                    value:
                      exp: Flow.IsWeekday
                - attribute:
                    name:
                      lit: Flow.IvrName
                    value:
                      exp: Flow.IvrName
                - attribute:
                    name:
                      lit: Flow.AccountNotes
                    value:
                      exp: Flow.AccountNotes
                - attribute:
                    name:
                      lit: Flow.ExtensionAttribute1
                    value:
                      exp: Flow.ExtensionAttribute1
                - attribute:
                    name:
                      lit: Flow.ExtensionAttribute2
                    value:
                      exp: Flow.ExtensionAttribute2
                - attribute:
                    name:
                      lit: Flow.ExtensionAttribute3
                    value:
                      exp: Flow.ExtensionAttribute3
          - setParticipantData:
              name: Set other Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.CoursesString
                    value:
                      exp: Flow.CoursesString
                - attribute:
                    name:
                      lit: Flow.LockReasonString
                    value:
                      exp: Flow.LockReasonString
                - attribute:
                    name:
                      lit: Flow.IdentifiedBy
                    value:
                      exp: Flow.IdentifiedBy
          - updateData:
              name: Update Flow log
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: Append(Flow.FlowLog, "Data successfully set. Attempting to transfer to Flow or ACD. \n**End of General Phone Task Flow**\n")
          - setParticipantData:
              name: Set Flow.FlowLog
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - decision:
              name: Flow Phone Testing - Future State
              condition:
                exp: Call.Ani=="<EMAIL>"
              outputs:
                "yes":
                  actions:
                    - disconnect:
                        name: Disconnect
                "no":
                  actions:
                    - switch:
                        name: Switch for MOTD routing
                        evaluate:
                          firstTrue:
                            default:
                              actions:
                                - updateData:
                                    name: Update Flow log
                                    statements:
                                      - string:
                                          variable: Flow.FlowLog
                                          value:
                                            exp: "Append(Flow.FlowLog, \"DEFAULT path taken. Flow.IvrName was not matched to: \"\n, Flow.IvrName\n,\". MOTD failure**\\n\")"
                            cases:
                              - case:
                                  value:
                                    exp: Flow.IvrName=="eSolutions IT Service Desk"
                                  actions:
                                    - updateData:
                                        name: "PCC: PCC_Duo_SD_Auth_On"
                                        statements:
                                          - boolean:
                                              variable: Flow.PCC_Duo_SD_Auth_On
                                              value:
                                                lit: true
                                    - decision:
                                        name: Decision
                                        condition:
                                          exp: Flow.IsValidDeakinObject and Flow.PCC_Duo_SD_Auth_On
                                        outputs:
                                          "yes":
                                            actions:
                                              - callTask:
                                                  name: Call Task
                                                  targetTaskRef: "/inboundCall/tasks/task[IT SDesk Duo Auth_15]"
                                    - transferToFlow:
                                        name: Transfer to Flow
                                        targetFlow:
                                          name: 2 - eSolutions MOTD
                                        failureOutputs:
                                          errorType:
                                            noValue: true
                                          errorMessage:
                                            noValue: true
                              - case:
                                  value:
                                    exp: Contains(Flow.IvrName, "DSA")
                                  actions:
                                    - transferToFlow:
                                        name: Transfer to Flow
                                        targetFlow:
                                          name: 2 - DSA MOTD
                                        failureOutputs:
                                          errorType:
                                            noValue: true
                                          errorMessage:
                                            noValue: true
                              - case:
                                  value:
                                    exp: Contains(Flow.IvrName,"PSEC")
                                  actions:
                                    - transferToFlow:
                                        name: Transfer to PSEC
                                        targetFlow:
                                          name: 2 - PSEC MOTD
                                        failureOutputs:
                                          errorType:
                                            noValue: true
                                          errorMessage:
                                            noValue: true
                              - case:
                                  value:
                                    exp: Flow.IvrName == "People Connect" or Flow.IvrName == "HR Help Line" or Flow.IvrName == "Deakin One Number"
                                  actions:
                                    - transferToFlow:
                                        name: Transfer to People Connect
                                        targetFlow:
                                          name: 2 - People Connect MOTD
                                        failureOutputs:
                                          errorType:
                                            noValue: true
                                          errorMessage:
                                            noValue: true
                              - case:
                                  value:
                                    exp: Contains(Flow.IvrName,"Campus Services")
                                  actions:
                                    - transferToFlow:
                                        name: Transfer to Campus Services
                                        targetFlow:
                                          name: 2 - Campus Services MOTD
                                        failureOutputs:
                                          errorType:
                                            noValue: true
                                          errorMessage:
                                            noValue: true
                              - case:
                                  value:
                                    exp: Contains(Flow.IvrName,"DSL")
                                  actions:
                                    - switch:
                                        name: DSL Switch
                                        evaluate:
                                          firstTrue:
                                            cases:
                                              - case:
                                                  value:
                                                    exp: Flow.IvrName=="DSL - HWB"
                                                  actions:
                                                    - transferToFlow:
                                                        name: Transfer to DSL - HWB MOTD
                                                        targetFlow:
                                                          name: 2 - DSL - HWB MOTD
                                                        failureOutputs:
                                                          errorType:
                                                            noValue: true
                                                          errorMessage:
                                                            noValue: true
                                              - case:
                                                  value:
                                                    exp: Flow.IvrName=="DSL - ISSO"
                                                  actions:
                                                    - transferToFlow:
                                                        name: Transfer to DSL - ISSO MOTD
                                                        targetFlow:
                                                          name: 2 - DSL - ISSO MOTD
                                                        failureOutputs:
                                                          errorType:
                                                            noValue: true
                                                          errorMessage:
                                                            noValue: true
                              - case:
                                  value:
                                    exp: Contains(Flow.IvrName,"Library")
                                  actions:
                                    - transferToFlow:
                                        name: Transfer to Library MOTD
                                        targetFlow:
                                          name: 2 - Library MOTD
                                        failureOutputs:
                                          errorType:
                                            noValue: true
                                          errorMessage:
                                            noValue: true
                              - case:
                                  value:
                                    exp: Contains(Flow.IvrName,"Domestic Admissions")
                                  actions:
                                    - transferToFlow:
                                        name: Transfer to Flow
                                        targetFlow:
                                          name: 2 - Domestic Admissions MOTD
                                        failureOutputs:
                                          errorType:
                                            noValue: true
                                          errorMessage:
                                            noValue: true
                              - case:
                                  value:
                                    exp: Flow.IvrName == "SSN - Priority Students"
                                  actions:
                                    - transferToFlow:
                                        name: Transfer to Flow
                                        targetFlow:
                                          name: 2 - SSN Priority Students MOTD
                                        failureOutputs:
                                          errorType:
                                            noValue: true
                                          errorMessage:
                                            noValue: true
                    - updateData:
                        name: Update Flow log
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: "Append(Flow.FlowLog,\n\"** \",\nFlow.IvrName,\n\" 1 - After Route to MOTD FAIL**\",\n\"\\n\")"
                    - setParticipantData:
                        name: Set Flow.FlowLog
                        attributes:
                          - attribute:
                              name:
                                lit: Flow.FlowLog
                              value:
                                exp: Flow.FlowLog
                    - setParticipantData:
                        name: Set Flow.FlowLog
                        attributes:
                          - attribute:
                              name:
                                lit: Flow.FlowLog
                              value:
                                exp: Flow.FlowLog
          - transferToFlow:
              name: Transfer to Flow
              targetFlow:
                name: Error Handling Flow
              failureOutputs:
                errorType:
                  noValue: true
                errorMessage:
                  noValue: true
          - playAudio:
              name: Play Audio
              audio:
                tts: We were unable to transfer your call. Please try again later.
          - disconnect:
              name: Disconnect
    - task:
        name: Welcome Task
        refId: Welcome Task_13
        variables:
          - booleanVariable:
              name: Task.IsManualClosure
              initialValue:
                lit: false
              isInput: false
              isOutput: false
          - booleanVariable:
              name: Task.IsWeekdayClosureTime
              initialValue:
                lit: false
              isInput: false
              isOutput: false
          - booleanVariable:
              name: Task.IsWeekendClosureTime
              initialValue:
                lit: false
              isInput: false
              isOutput: false
          - booleanVariable:
              name: Task.TransferToClosureFlow
              initialValue:
                lit: false
              isInput: false
              isOutput: false
          - booleanVariable:
              name: Task.TransferToTSMobileFlow
              initialValue:
                lit: false
              isInput: false
              isOutput: false
        actions:
          - callCommonModule:
              name: Call Common Module
              commonModule:
                Production Analysis (DEV):
                  ver_latestPublished:
                    inputs:
                      Common.MediaType:
                        lit: voice
                      Common.CallAni:
                        exp: Call.Ani
                      Common.FlowLog_InQueue:
                        noValue: true
                    outputs:
                      Common.IsDev:
                        var: Flow.IsDev
                      Common.IsTechnicalGroup:
                        noValue: true
                      Common.FlowLog_InQueue:
                        noValue: true
          - updateData:
              name: Update Data
              statements:
                - boolean:
                    variable: Flow.PhoneBridgeGroupsLoopRun
                    value:
                      lit: false
                - boolean:
                    variable: Flow.IsDevInv
                    value:
                      exp: GetPromptDuration(Prompt.PureCloudWelcome_DONOTTOUCH)<ToDuration("PT3S")
                - string:
                    variable: Flow.CallAni
                    value:
                      exp: Call.Ani
                - string:
                    variable: Flow.CalledAddressOriginal
                    value:
                      exp: Call.CalledAddressOriginal
                - string:
                    variable: Flow.ConversationID
                    value:
                      exp: Call.ConversationId
                - string:
                    variable: Flow.AniDiallingCode
                    value:
                      exp: ToPhoneNumber(Call.Ani).dialingCode
          - decision:
              name: Decision
              condition:
                exp: Flow.IsDev
              outputs:
                "yes":
                  actions:
                    - playAudio:
                        name: Play Audio
                        audio:
                          exp: Prompt.PureCloudWelcome_DONOTTOUCH
          - dataTableLookup:
              name: Data Table Lookup
              lookupValue:
                exp: Call.Ani
              dataTable:
                Testing Numbers:
                  failureOutputs:
                    errorType:
                      noValue: true
                    errorMessage:
                      noValue: true
              outputs:
                found:
                  actions:
                    - updateData:
                        name: Update Data
                        statements:
                          - boolean:
                              variable: Flow.TestingNumber
                              value:
                                lit: true
                    - setParticipantData:
                        name: Set Participant Data
                        attributes:
                          - attribute:
                              name:
                                lit: Flow.TestingNumber
                              value:
                                exp: Flow.TestingNumber
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.IsDev
                    value:
                      exp: Flow.IsDev
                - attribute:
                    name:
                      lit: Flow.IsDevDuration
                    value:
                      exp: Flow.IsDevDuration
                - attribute:
                    name:
                      lit: Flow.IsDevInv
                    value:
                      exp: Flow.IsDevInv
                - attribute:
                    name:
                      lit: Flow.CallAni
                    value:
                      exp: Flow.CallAni
                - attribute:
                    name:
                      lit: Flow.CalledAddressOriginal
                    value:
                      exp: Flow.CalledAddressOriginal
                - attribute:
                    name:
                      lit: Flow.ConversationID
                    value:
                      exp: Flow.ConversationID
                - attribute:
                    name:
                      lit: Flow.AniDiallingCode
                    value:
                      exp: Flow.AniDiallingCode
          - updateData:
              name: Start Flow log
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(\n\"**Start of Phone/ID Data Action Flow**\", \"\\n\",\n\"Conversation ID is: \", Call.ConversationId, \"\\n\",\n\"Current environment is : \", If(Flow.IsDev,\"Development\",\"Production\"), \"\\n\",\n\"DNIS set to: \", Call.CalledAddress, \"\\n\",\n\"DNIS Original set to: \", Call.CalledAddressOriginal, \"\\n\"\n)"
          - setParticipantData:
              name: Set FlowLog
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - updateData:
              name: Set time booleans
              statements:
                - boolean:
                    variable: Task.IsManualClosure
                    value:
                      lit: false
                - boolean:
                    variable: Task.IsWeekdayClosureTime
                    value:
                      lit: false
                - boolean:
                    variable: Task.IsWeekendClosureTime
                    value:
                      lit: false
                - boolean:
                    variable: Task.TransferToTSMobileFlow
                    value:
                      lit: false
                - boolean:
                    variable: Task.TransferToClosureFlow
                    value:
                      lit: false
          - updateData:
              name: Update Flow log
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog, \"Attempting Phone bridge with number: \", Call.Ani, \"\\n\")"
          - setParticipantData:
              name: Set FlowLog
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - callData:
              name: Get Object by Phone Number-
              processingPrompt:
                lit:
                  name: PromptSystem.on_hold_music
              timeout:
                lit:
                  minutes: 1
              category:
                Web Services Data Actions - Deakin:
                  dataAction:
                    GetDeakinObjectByPhoneNumber-v110:
                      inputs:
                        PhoneNumber:
                          exp: Call.Ani
                        Identity:
                          exp: Call.CalledAddressOriginal
                        ConversationId:
                          exp: Call.ConversationId
                        Context:
                          lit: 1 - Phone Bridge and Welcome
                      successOutputs:
                        Id:
                          var: Flow.Id
                        Username:
                          var: Flow.Username
                        DisplayName:
                          var: Flow.DisplayName
                        AccountType:
                          var: Flow.AccountType
                        Campus:
                          var: Flow.Campus
                        PhoneNumber:
                          var: Flow.PhoneNumber
                        PhoneMobile:
                          var: Flow.PhoneMobile
                        PhoneAdditional:
                          var: Flow.PhoneAdditional
                        Email:
                          var: Flow.Email
                        SipAddress:
                          var: Flow.SipAddress
                        AccountEnabled:
                          var: Flow.AccountEnabled
                        LockReason:
                          var: Flow.LockReason
                        PasswordExpiresInDays:
                          var: Flow.PasswordExpiresInDays
                        Courses:
                          var: Flow.Courses
                        PhoneType:
                          var: Flow.PhoneType
                        ServiceNowSysId:
                          var: Flow.ServiceNowSysId
                        IsValidDeakinObject:
                          var: Flow.IsValidDeakinObject
                        IsVIP:
                          var: Flow.IsVIP
                        IsExecutive:
                          var: Flow.IsExecutive
                        IsTechnicalGroup:
                          var: Flow.IsTechnicalGroup
                        HasITAccount:
                          var: Flow.HasITAccount
                        TimeOfDay:
                          var: Flow.TimeOfDay
                        DayOfWeek:
                          var: Flow.DayOfWeek
                        UtcOffsetMinutes:
                          var: Flow.UTCOffsetMinutes
                        IsWeekday:
                          var: Flow.IsWeekday
                        IvrName:
                          var: Flow.IvrName
                        MemberOfGroups:
                          var: Flow.MemberOfGroups
                        AccountNotes:
                          var: Flow.AccountNotes
                        ExtensionAttribute1:
                          var: Flow.ExtensionAttribute1
                        ExtensionAttribute2:
                          var: Flow.ExtensionAttribute2
                        ExtensionAttribute3:
                          var: Flow.ExtensionAttribute3
              outputs:
                success:
                  actions:
                    - decision:
                        name: Decision
                        condition:
                          exp: Flow.IsDev
                        outputs:
                          "yes":
                            actions:
                              - updateData:
                                  name: Update Data
                                  statements:
                                    - string:
                                        variable: Flow.IvrName
                                        value:
                                          exp: "If(Call.CalledAddressOriginal==\"+61352712504\",\"Library\",\nIf(Call.CalledAddressOriginal==\"+61352258005\",\"Campus Services\",\nIf(Call.CalledAddressOriginal==\"+61352258006\",\"BL Student Areas\",\nFlow.IvrName\n  )\n  )\n  )\n\n"
                              - updateData:
                                  name: Update Flow log Delete me
                                  statements:
                                    - string:
                                        variable: Flow.FlowLog
                                        value:
                                          exp: "Append(Flow.FlowLog,\"Call.CalledAddressOriginal==\",Call.CalledAddressOriginal,\n\"Flow.IvrName=\", Flow.IvrName\n,\"\\n\")\n\n"
                              - setParticipantData:
                                  name: Set FlowLog
                                  attributes:
                                    - attribute:
                                        name:
                                          lit: Flow.FlowLog
                                        value:
                                          exp: Flow.FlowLog
                failure:
                  actions:
                    - updateData:
                        name: Set report bridge event
                        statements:
                          - boolean:
                              variable: Flow.ReportBridgeEvent
                              value:
                                lit: true
                    - updateData:
                        name: Update Flow log
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: Append(Flow.FlowLog,"67 - Call bridge failed from ",Call.Ani," at ",ToString(AddHours(GetCurrentDateTimeUtc(),10)),"\n")
                timeout:
                  actions:
                    - updateData:
                        name: Set report bridge event
                        statements:
                          - boolean:
                              variable: Flow.ReportBridgeEvent
                              value:
                                lit: true
                    - updateData:
                        name: Update Flow log
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: Append(Flow.FlowLog,"Call bridge timed out from ",Call.Ani," at ",ToString(AddHours(GetCurrentDateTimeUtc(),10)),"\n")
          - decision:
              name: Decision
              condition:
                exp: Flow.ReportBridgeEvent
              outputs:
                "yes":
                  actions:
                    - callData:
                        name: Call Data Action
                        processingPrompt:
                          lit:
                            name: PromptSystem.on_hold_music
                        timeout:
                          lit:
                            minutes: 1
                        category:
                          Web Services Data Actions - Deakin:
                            dataAction:
                              ReportBridgeActionEvent-v110:
                                inputs:
                                  Identity:
                                    exp: Call.CalledAddressOriginal
                                  ConversationId:
                                    exp: Call.ConversationId
                                  Context:
                                    lit: Error Handling Flow
                                  Message:
                                    exp: Append(Flow.FlowLog,"\nCall bridge failed from ",Call.Ani,".")
                                  ResetStatusCounters:
                                    noValue: true
                                successOutputs:
                                  RequestsProcessed:
                                    noValue: true
                                  SlowRequestsProcessed:
                                    noValue: true
                                  ExceptionCount:
                                    noValue: true
                                  LastException:
                                    noValue: true
                                  BridgeActionEvents:
                                    noValue: true
                                  ProcessStartTime:
                                    noValue: true
                                  DataLastRefreshedAt:
                                    noValue: true
                                  DataLastModified:
                                    noValue: true
                                  DataLastRefreshMessage:
                                    noValue: true
                                  DataLastRefreshTimeTaken:
                                    noValue: true
                                  DataRecordCount:
                                    noValue: true
                                  Version:
                                    noValue: true
                                  PID:
                                    noValue: true
          - dataTableLookup:
              name: Data Table Lookup
              lookupValue:
                exp: Call.Ani
              dataTable:
                Special Number Lookup:
                  foundOutputs:
                    SpecialPhoneLocation:
                      var: Flow.DisplayName
                    SpecialValue:
                      noValue: true
                    SpecialAdditionalValue:
                      noValue: true
                    SpecialPhoneType:
                      var: Flow.PhoneType
                  failureOutputs:
                    errorType:
                      noValue: true
                    errorMessage:
                      noValue: true
              outputs:
                found:
                  actions:
                    - updateData:
                        name: Update Data
                        statements:
                          - boolean:
                              variable: Flow.TestingNumber
                              value:
                                exp: If(Flow.PhoneType  == "Testing", true,false)
                          - boolean:
                              variable: Flow.IsValidDeakinObject
                              value:
                                lit: true
                    - updateData:
                        name: Update Flow log
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: "Append(Flow.FlowLog\n, \"Testing number is: \", ToString(Flow.TestingNumber)\n, \"Call.Ani is: \",Call.Ani\n, \"Flow.PhoneType is: \",Flow.PhoneType\n, \"PhoneType was found in data lookup. Setting IsValidDeakinObject to true\"\n,\"\\n\")"
          - setParticipantData:
              name: Set FlowLog
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
                - attribute:
                    name:
                      lit: Flow.IdentifiedBy
                    value:
                      exp: "\"\""
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.TimeOfDay
                    value:
                      exp: Flow.TimeOfDay
                - attribute:
                    name:
                      lit: Flow.CalledAddress
                    value:
                      exp: Call.CalledAddressOriginal
                - attribute:
                    name:
                      lit: Flow.IdentifiedBy
                    value:
                      exp: Flow.IdentifiedBy
                - attribute:
                    name:
                      lit: Flow.IvrName
                    value:
                      exp: Flow.IvrName
                - attribute:
                    name:
                      lit: Flow.PhoneType
                    value:
                      exp: Flow.PhoneType
                - attribute:
                    name:
                      lit: Flow.TestingNumber
                    value:
                      exp: Flow.TestingNumber
          - switch:
              name: Transfer to respective queue area.
              evaluate:
                firstTrue:
                  default:
                    actions:
                      - playAudio:
                          name: Play Default Audio
                          audio:
                            exp: Prompt.GeneralWelcome
                  cases:
                    - case:
                        value:
                          exp: "Flow.IvrName == \"eSolutions IT Service Desk\" \n\n"
                        actions:
                          - evaluateScheduleGroup:
                              name: Evaluate TS Schedule Group
                              inServiceSchedules:
                                noValue: true
                              evaluate:
                                now: true
                              scheduleGroup:
                                lit:
                                  name: eSolutions Teaching Space Support
                                name: eSolutions Teaching Space Support
                              emergencyGroup:
                                noValue: true
                              outputs:
                                open:
                                  actions:
                                    - decision:
                                        name: Decision
                                        condition:
                                          exp: "(Flow.PhoneType == \"TS\" or Flow.PhoneType == \"SHRD\" or Call.Ani == \"tel:+61352712511\")"
                                        outputs:
                                          "yes":
                                            actions:
                                              - updateData:
                                                  name: Update Data
                                                  statements:
                                                    - boolean:
                                                        variable: Task.TransferToTSMobileFlow
                                                        value:
                                                          lit: true
                                              - updateData:
                                                  name: Update Flow log
                                                  statements:
                                                    - string:
                                                        variable: Flow.FlowLog
                                                        value:
                                                          exp: Append(Flow.FlowLog, "Phone Bridge Transferring to Teaching Space Mobile Flow.\n")
                                              - setParticipantData:
                                                  name: Set Flow log
                                                  attributes:
                                                    - attribute:
                                                        name:
                                                          lit: Flow.FlowLog
                                                        value:
                                                          exp: Flow.FlowLog
                                              - transferToFlow:
                                                  name: Transfer to Flow
                                                  targetFlow:
                                                    name: 5e - eSolutions - Teaching Space Mobile Transfer
                                                  failureOutputs:
                                                    errorType:
                                                      noValue: true
                                                    errorMessage:
                                                      noValue: true
                                          "no":
                                            actions:
                                              - transferToFlow:
                                                  name: Transfer to Flow
                                                  targetFlow:
                                                    name: 99 - General Closure
                                                  failureOutputs:
                                                    errorType:
                                                      noValue: true
                                                    errorMessage:
                                                      noValue: true
                          - playAudio:
                              name: Play Audio
                              audio:
                                exp: Prompt.eSolutionsWelcome
                    - case:
                        value:
                          exp: Contains(Flow.IvrName, "DSA")
                        actions:
                          - playAudio:
                              name: Play DSAWelcome Audio
                              audio:
                                exp: Prompt.DSAWelcome
                          - updateData:
                              name: Update Flow log
                              statements:
                                - string:
                                    variable: Flow.FlowLog
                                    value:
                                      exp: "Append(Flow.FlowLog, \"DSA Area Called: \", Flow.IvrName, \".\\n\")"
                    - case:
                        value:
                          exp: Flow.IvrName=="PSEC"
                        actions:
                          - callTask:
                              name: Call Task
                              targetTaskRef: "/inboundCall/tasks/task[PSEC Dev IVRName Menu_14]"
                          - switch:
                              name: Play PSEC welcome audio
                              evaluate:
                                firstTrue:
                                  default:
                                    actions:
                                      - playAudio:
                                          name: Play PSEC general welcome Audio
                                          audio:
                                            exp: Prompt.GeneralWelcome
                                  cases:
                                    - case:
                                        value:
                                          exp: Flow.IvrName=="PSEC - Outbound"
                                        actions:
                                          - playAudio:
                                              name: Play PSEC Outbound Audio
                                              audio:
                                                exp: Prompt.PSECOutboundReturnedCall
                                    - case:
                                        value:
                                          exp: Flow.IvrName=="PSEC - International"
                                        actions:
                                          - playAudio:
                                              name: Play Audio
                                              audio:
                                                exp: Prompt.GeneralWelcome
                    - case:
                        value:
                          exp: Flow.IvrName == "People Connect" or Flow.IvrName == "HR Help Line" or Flow.IvrName == "Deakin One Number"
                        actions:
                          - playAudio:
                              name: Play Audio
                              audio:
                                exp: AudioPlaybackOptions(Append(ToAudioBlank(100), ToAudio(Prompt.TTSThankyouforcontactingPeopleConnectNeural2B), ToAudioBlank(100)), true)
                    - case:
                        value:
                          exp: Contains(Flow.IvrName,"Campus Services")
                        actions:
                          - playAudio:
                              name: Play Audio
                              audio:
                                exp: Prompt.ThankyouforcallingDeakinsParkingandTransportteam
                    - case:
                        value:
                          exp: Contains(Flow.IvrName,"DSL")
                        actions:
                          - switch:
                              name: Determine which DSL queue
                              evaluate:
                                firstTrue:
                                  default:
                                    actions:
                                      - playAudio:
                                          name: Play Audio
                                          audio:
                                            exp: Prompt.GeneralWelcome
                                  cases:
                                    - case:
                                        value:
                                          exp: Flow.IvrName=="DSL - HWB"
                                        actions:
                                          - playAudio:
                                              name: Play Audio
                                              audio:
                                                exp: Prompt.HWB_Welcome
                                    - case:
                                        value:
                                          exp: Flow.IvrName=="DSL - ISSO"
                                        actions:
                                          - playAudio:
                                              name: Play Audio
                                              audio:
                                                exp: Prompt.DSL_ISSOWelcome
                    - case:
                        value:
                          exp: Flow.IvrName=="Library"
                        actions:
                          - playAudio:
                              name: Play Library Audio
                              audio:
                                exp: Prompt.LibraryWelcome
                    - case:
                        value:
                          exp: Contains(Flow.IvrName,"Domestic Admissions")
                        actions:
                          - playAudio:
                              name: Play Domestic Admissions welcome audio
                              audio:
                                exp: Prompt.GeneralWelcome
                    - case:
                        value:
                          exp: Flow.IvrName == "SSN - Priority Students"
                        actions:
                          - playAudio:
                              name: Play DSAWelcome Audio
                              audio:
                                exp: Prompt.DSAWelcome
          - updateData:
              name: Update Flow log
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: Append(Flow.FlowLog, "Moving to Groups Loop, \n")
          - setParticipantData:
              name: Set Participant Data
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - jumpToTask:
              name: Jump to ID Task
              targetTaskRef: "/inboundCall/tasks/task[Groups Loop_11]"
    - task:
        name: PSEC Dev IVRName Menu
        refId: PSEC Dev IVRName Menu_14
        variables:
          - stringVariable:
              name: Task.IsDevQueue
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - integerVariable:
              name: Task.IsDevQueueLoopIndex
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
        actions:
          - decision:
              name: Are we in dev?
              condition:
                exp: Flow.IsDev
              outputs:
                "yes":
                  actions:
                    - loop:
                        name: Loop
                        currentIndex:
                          var: Task.IsDevQueueLoopIndex
                        loopCount:
                          lit: 2
                        outputs:
                          loop:
                            actions:
                              - decision:
                                  name: Is this a PSEC queue?
                                  condition:
                                    exp: Contains(Flow.IvrName, "PSEC")
                                  outputs:
                                    "yes":
                                      actions:
                                        - collectInput:
                                            name: Get PSEC IVR chooser input
                                            inputData:
                                              var: Task.IsDevQueue
                                            digits:
                                              exact: 1
                                            interDigitTimeout:
                                              noValue: true
                                            noEntryTimeout:
                                              noValue: true
                                            inputAudio:
                                              exp: AudioPlaybackOptions(Append(ToAudio(Prompt.PSEC_DEV_Menu_Prompts), ToAudioBlank(250)), true)
                                            acceptJustStar:
                                              lit: false
                                            acceptJustPound:
                                              lit: false
                                            verificationType: none
                                            outputs:
                                              success:
                                                actions:
                                                  - decision:
                                                      name: Is this a valid input (1-5) ?
                                                      condition:
                                                        exp: ToInt(Task.IsDevQueue)==(1) or ToInt(Task.IsDevQueue)==(2) or ToInt(Task.IsDevQueue)==(3) or ToInt(Task.IsDevQueue)==(4) or ToInt(Task.IsDevQueue)==(5)
                                                      outputs:
                                                        "yes":
                                                          actions:
                                                            - updateData:
                                                                name: Update Task.IsDevQueue variable
                                                                statements:
                                                                  - string:
                                                                      variable: Flow.IvrName
                                                                      value:
                                                                        exp: "If(Task.IsDevQueue==\"2\",\"PSEC - Outbound\",\n\tIf(Task.IsDevQueue==\"3\",\"PSEC - Conversion\",\n\t\tIf(Task.IsDevQueue==\"4\",\"PSEC - Course Specialists\",\n                       If(Task.IsDevQueue==\"5\",\"PSEC - International\",\n\t\t\t\"PSEC\"\n                         )\n\t\t)\n\t)\n)"
                                                            - setParticipantData:
                                                                name: Set Participant Data
                                                                attributes:
                                                                  - attribute:
                                                                      name:
                                                                        lit: Flow.IvrName
                                                                      value:
                                                                        exp: Flow.IvrName
                                                            - loopExit:
                                                                name: Exit Loop
                              - playAudio:
                                  name: Play Invalid selection
                                  audio:
                                    tts: Invalid number entered
          - updateData:
              name: Update Flow log
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog,\"PSEC Dev IvrName Menu has set IVRName to: \", Flow.IvrName,\"\\n\")"
          - endTask:
              name: PSEC Dev IVR menu
              outputPath:
                name: default
    - task:
        name: IT SDesk Duo Auth
        refId: IT SDesk Duo Auth_15
        variables:
          - stringVariable:
              name: Task.AuthResult
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: Task.AuthStatus
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: Task.AuthStatusMessage
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
          - stringVariable:
              name: Task.DeakinIDEntered
              initialValue:
                lit: ""
              isInput: false
              isOutput: false
          - booleanVariable:
              name: Task.DUOAuthResult
              initialValue:
                noValue: true
              isInput: false
              isOutput: false
        actions:
          - updateData:
              name: Update Flow log
              statements:
                - string:
                    variable: Flow.FlowLog
                    value:
                      exp: "Append(Flow.FlowLog\n,\"Flow.PCC_Duo_SD_Auth_On is: \", Tostring(Flow.PCC_Duo_SD_Auth_On)\n, \"and Flow.Ivrname is: \", Flow.IvrName\n,\"\\n\")"
          - setParticipantData:
              name: Set Flow.FlowLog
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - decision:
              name: Decision
              condition:
                exp: Flow.PCC_Duo_SD_Auth_On
              outputs:
                "yes":
                  actions:
                    - callCommonModule:
                        name: Call Common Module
                        commonModule:
                          AuthenticateViaDUO:
                            ver_latestPublished:
                              inputs:
                                Common.FlowLog:
                                  exp: Flow.FlowLog
                                Common.FlowUsername:
                                  exp: Flow.Username
                                Common.Ivrname:
                                  noValue: true
                                Common.PCC_Duo_SD_Auth_On:
                                  noValue: true
                              outputs:
                                Common.AuthCallStat:
                                  noValue: true
                                Common.AuthResult:
                                  var: Task.AuthResult
                                Common.AuthStatus:
                                  var: Task.AuthStatus
                                Common.AuthStatusMessage:
                                  var: Task.AuthStatusMessage
                                Common.DUOAuthResult:
                                  var: Task.DUOAuthResult
                                Common.FlowLog:
                                  var: Flow.FlowLog2
                    - updateData:
                        name: Update Data
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: Append(Flow.FlowLog, Flow.FlowLog2, "\n")
                    - setParticipantData:
                        name: Set Participant Data
                        attributes:
                          - attribute:
                              name:
                                lit: Flow.FlowLog
                              value:
                                exp: Flow.FlowLog
                    - updateData:
                        name: Update Flow log
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: Append(Flow.FlowLog,"**Phone Bridge returned ", Task.AuthResult , " at DUO Authentication**","\n")
                    - setParticipantData:
                        name: Set Flow.FlowLog
                        attributes:
                          - attribute:
                              name:
                                lit: Flow.FlowLog
                              value:
                                exp: Flow.FlowLog
                    - decision:
                        name: Decision
                        condition:
                          exp: Task.AuthResult=="allow"
                        outputs:
                          "no":
                            actions:
                              - callData:
                                  name: Report Bridge Action
                                  processingPrompt:
                                    noValue: true
                                  timeout:
                                    lit:
                                      minutes: 1
                                  category:
                                    Web Services Data Actions - Deakin:
                                      dataAction:
                                        ReportBridgeActionEvent-v110:
                                          inputs:
                                            Identity:
                                              exp: Call.CalledAddressOriginal
                                            ConversationId:
                                              exp: Call.ConversationId
                                            Context:
                                              lit: 3 - ID Number Bridge after MOTD failed
                                            Message:
                                              exp: Flow.FlowLog
                                            ResetStatusCounters:
                                              noValue: true
                                          successOutputs:
                                            RequestsProcessed:
                                              noValue: true
                                            SlowRequestsProcessed:
                                              noValue: true
                                            ExceptionCount:
                                              noValue: true
                                            LastException:
                                              noValue: true
                                            BridgeActionEvents:
                                              noValue: true
                                            ProcessStartTime:
                                              noValue: true
                                            DataLastRefreshedAt:
                                              noValue: true
                                            DataLastModified:
                                              noValue: true
                                            DataLastRefreshMessage:
                                              noValue: true
                                            DataLastRefreshTimeTaken:
                                              noValue: true
                                            DataRecordCount:
                                              noValue: true
                                            Version:
                                              noValue: true
                                            PID:
                                              noValue: true
                                  outputs:
                                    success:
                                      actions:
                                        - updateData:
                                            name: Update Flow log
                                            statements:
                                              - string:
                                                  variable: Flow.FlowLog
                                                  value:
                                                    exp: Append(Flow.FlowLog,"\nDUO Identification successful but not approved\n")
                                    failure:
                                      actions:
                                        - updateData:
                                            name: Update Flow log
                                            statements:
                                              - string:
                                                  variable: Flow.FlowLog
                                                  value:
                                                    exp: Append(Flow.FlowLog,"\nDUO Authentication failed for ",Task.DeakinIDEntered,".")
                                    timeout:
                                      actions:
                                        - updateData:
                                            name: Update Flow log
                                            statements:
                                              - string:
                                                  variable: Flow.FlowLog
                                                  value:
                                                    exp: Append(Flow.FlowLog,"\nDuo Authentication timed out for",Task.DeakinIDEntered,".")
                              - setParticipantData:
                                  name: Set Flow.FlowLog
                                  attributes:
                                    - attribute:
                                        name:
                                          lit: Flow.FlowLog
                                        value:
                                          exp: Flow.FlowLog
                    - setParticipantData:
                        name: Set Participant Data
                        attributes:
                          - attribute:
                              name:
                                lit: Flow.AuthResult
                              value:
                                exp: Task.AuthResult
                    - updateData:
                        name: Update Flow log
                        statements:
                          - string:
                              variable: Flow.FlowLog
                              value:
                                exp: "Append(Flow.FlowLog,\"Auth is switched on for: \", Flow.IvrName,\"\\n\")"
          - setParticipantData:
              name: Set Flow.FlowLog
              attributes:
                - attribute:
                    name:
                      lit: Flow.FlowLog
                    value:
                      exp: Flow.FlowLog
          - endTask:
              name: End Task
              outputPath:
                name: default

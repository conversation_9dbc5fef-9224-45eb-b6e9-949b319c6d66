botFlow:
  name: 1 - <PERSON> Bot - Get Details
  description: Bot flow to get user details prior to message flow..
  division: Student Central Hubs
  startUpRef: "/botFlow/bots/bot[Default Bot_10]"
  defaultLanguage: en-au
  supportedLanguages:
    en-au:
      defaultLanguageSkill:
        noValue: true
      speechToText:
        engine:
          name: Genesys Enhanced v2
          defaultEngine: true
  variables:
    - booleanVariable:
        name: Flow.AccountEnabled
        initialValue:
          lit: false
        isInput: false
        isOutput: false
    - stringCollectionVariable:
        name: Flow.AccountNotes
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.AccountType
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.Campus
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringCollectionVariable:
        name: Flow.Courses
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - integerVariable:
        name: Flow.DayOfWeek
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.DisplayName
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.Email
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.ExtensionAttribute1
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.ExtensionAttribute2
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.ExtensionAttribute3
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.FlowLog
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - booleanVariable:
        name: Flow.HasITAccount
        initialValue:
          lit: false
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.Id
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.IDNumber
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - booleanVariable:
        name: Flow.IsExecutive
        initialValue:
          lit: false
        isInput: false
        isOutput: false
    - booleanVariable:
        name: Flow.IsTechnicalGroup
        initialValue:
          lit: false
        isInput: false
        isOutput: false
    - booleanVariable:
        name: Flow.IsValidDeakinObject
        initialValue:
          lit: false
        isInput: false
        isOutput: false
    - booleanVariable:
        name: Flow.IsVIP
        initialValue:
          lit: false
        isInput: false
        isOutput: false
    - booleanVariable:
        name: Flow.IsWeekday
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.IvrName
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringCollectionVariable:
        name: Flow.LockReason
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringCollectionVariable:
        name: Flow.MemberOfGroups
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.MessageGreeting
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.MessageIDNumber
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.MessageNameAndDOB
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.MessageNameAndEmail
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.MessageReasonForCalling
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - integerVariable:
        name: Flow.PasswordExpiresInDays
        initialValue:
          lit: 0
        isInput: false
        isOutput: false
    - stringCollectionVariable:
        name: Flow.PhoneAdditional
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.PhoneMobile
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.PhoneNumber
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.PhoneType
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.ServiceNowSysId
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.SipAddress
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - integerVariable:
        name: Flow.TimeOfDay
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.Username
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - integerVariable:
        name: Flow.UTCOffsetMinutes
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Flow.WelcomePrivacy
        initialValue:
          noValue: true
        isInput: false
        isOutput: false
    - stringVariable:
        name: Slot.IDNumber
        initialValue:
          noValue: true
        isInput: true
        isOutput: true
    - stringVariable:
        name: Slot.NameAndDOB
        initialValue:
          noValue: true
        isInput: true
        isOutput: true
    - stringVariable:
        name: Slot.NameAndEmail
        initialValue:
          noValue: true
        isInput: true
        isOutput: true
    - stringVariable:
        name: Slot.ReasonForCalling
        initialValue:
          noValue: true
        isInput: true
        isOutput: true
  settingsActionDefaults:
    askForIntent:
      voiceEndOfInputTimeout:
        lit:
          seconds: 1
    askForBoolean:
      voiceEndOfInputTimeout:
        lit:
          seconds: 1
    askForSlot:
      voiceEndOfInputTimeout:
        lit:
          seconds: 1
    loopAnythingElse:
      voiceEndOfInputTimeout:
        lit:
          seconds: 1
  settingsErrorHandling:
    errorHandling:
      exit:
        none: true
    enableAgentEscalation:
      lit: true
    errorEventHandover:
      exp: "MakeCommunication(\n  \"Sorry, an error occurred. One moment, please, while I put you through to someone who can help.\")"
    agentEscalationConfirmation:
      exp: "MakeCommunication(\n  \"You want to speak to an advisor. Is that correct?\")"
    agentEscalationHandover:
      exp: "MakeCommunication(\n  \"One moment, please, and I will put you through to someone.\")"
    recognitionFailureEventHandover:
      exp: "MakeCommunication(\n  \"Sorry, I'm having trouble understanding you. One moment, please, while I put you through to someone who can help.\")"
    recognitionFailureEventHandling:
      exit:
        none: true
    agentEscalationHandling:
      exit:
        none: true
  settingsPrompts:
    ensureAudioInPrompts: false
    promptMediaToValidate:
      - mediaType: audio
      - mediaType: tts
  settingsBotFlow:
    intentSettings:
      - intent:
          confirmation:
            exp: "MakeCommunication(\n  \"I think you want assistance with deferring, is that correct?\")"
          name: Defer
      - intent:
          confirmation:
            exp: "MakeCommunication(\n  \"I think you want encumbrance assistance, is that correct?\")"
          name: Encumbrance
      - intent:
          confirmation:
            exp: "MakeCommunication(\n  \"I think you want enrolment assistance, is that correct?\")"
          name: Enrol
      - intent:
          confirmation:
            exp: "MakeCommunication(\n  \"I think you want to discuss fees, is that correct?\")"
          name: Fees
      - intent:
          confirmation:
            exp: "MakeCommunication(\n  \"We were unable to match your answer with a common response. \")"
          name: No intent
  settingsUserInput:
    enableBargeIn:
      lit: true
    enableAutomaticQuickReplies:
      lit: true
    noMatchesMax:
      lit: 0
    noInputsMax:
      lit: 3
    noInputsTimeout:
      lit:
        seconds: 7
    speechToTextSpeechDetectionSensitivity:
      noValue: true
    speechToTextMaxSpeechTimeout:
      noValue: true
    enableIntentClassificationHinting:
      lit: true
    confirmationRejectionsMax:
      lit: 3
    collectionLowConfidenceThreshold:
      lit: 20
    collectionHighConfidenceThreshold:
      lit: 70
    confirmationLowConfidenceThreshold:
      lit: 40
    noMatchApology:
      exp: "MakeCommunication(\n  \"Sorry.\")"
    noInputApology:
      exp: "MakeCommunication(\n  \"Sorry, I didn't hear you.\")"
    noToConfirmationApology:
      exp: "MakeCommunication(\n  \"My mistake.\")"
    confirmationNoMatchApology:
      exp: "MakeCommunication(\n  \"Sorry, please say yes or no.\")"
    confirmationNoInputApology:
      exp: "MakeCommunication(\n  \"Sorry, I didn't hear you.  Please say yes or no.\")"
  settingsKnowledge:
    none: true
  bots:
    - bot:
        name: Default Bot
        refId: Default Bot_10
        variables:
          - stringVariable:
              name: Bot.DeakinIDEntered
              initialValue:
                lit: ""
              isInput: false
              isOutput: false
        actions:
          - dataTableLookup:
              name: Data Table Lookup
              lookupValue:
                lit: standard
              dataTable:
                Student Central Messages:
                  foundOutputs:
                    WelcomePrivacy:
                      var: Flow.WelcomePrivacy
                    Greeting:
                      noValue: true
                    MessageOfTheDay1:
                      noValue: true
                    MessageOfTheDay2:
                      noValue: true
                    NameAndDOB:
                      var: Flow.MessageNameAndDOB
                    IDNumber:
                      var: Flow.MessageIDNumber
                    NameAndEmail:
                      var: Flow.MessageNameAndEmail
                    ReasonForCalling:
                      var: Flow.MessageReasonForCalling
                    TransferToAdviser:
                      noValue: true
                    ClosureMessage:
                      noValue: true
                    Spare1:
                      noValue: true
                    Spare2:
                      noValue: true
                    Spare3:
                      noValue: true
                    Spare4:
                      noValue: true
                    Spare5:
                      noValue: true
                    SpareBoolean:
                      noValue: true
                  failureOutputs:
                    errorType:
                      noValue: true
                    errorMessage:
                      noValue: true
          - communicate:
              name: Communicate greeting
              communication:
                exp: "MakeCommunication(\n Flow.MessageGreeting)"
          - decision:
              name: Decision
              condition:
                exp: IsNotSetOrEmpty(Flow.MessageIDNumber)
              outputs:
                "no":
                  actions:
                    - askForSlot:
                        name: Get ID slot
                        slot:
                          name: IDNumber
                        question:
                          exp: "MakeCommunication(\nFlow.MessageIDNumber)"
                        customValidation:
                          noValue: true
                        confirmationMode:
                          lit: never
                        noInput:
                          exp: "MakeCommunication(\n  \"Please say or enter the (please put your slot name here).\")"
                        noMatch:
                          exp: "MakeCommunication(\n  \"Please give me the (please put your slot name here) again.\")"
                        voiceEndOfInputTimeout:
                          noValue: true
                        outputs:
                          maxNoInputs:
                            enabled: false
                          maxNoMatches:
                            enabled: false
                    - updateData:
                        name: Update Data
                        statements:
                          - string:
                              variable: Flow.IDNumber
                              value:
                                exp: Slot.IDNumber
                    - callData:
                        name: Call Data Action
                        timeout:
                          lit:
                            minutes: 1
                        category:
                          Web Services Data Actions - Deakin:
                            dataAction:
                              GetDeakinObjectById-v110:
                                inputs:
                                  IdNumber:
                                    exp: Slot.IDNumber
                                  Identity:
                                    noValue: true
                                  ConversationId:
                                    exp: Session.ConversationId
                                  Context:
                                    lit: 3 - ID NumberBridge after MOTD
                                successOutputs:
                                  Id:
                                    var: Flow.Id
                                  Username:
                                    var: Flow.Username
                                  DisplayName:
                                    var: Flow.DisplayName
                                  AccountType:
                                    var: Flow.AccountType
                                  Campus:
                                    var: Flow.Campus
                                  PhoneNumber:
                                    var: Flow.PhoneNumber
                                  PhoneMobile:
                                    var: Flow.PhoneMobile
                                  PhoneAdditional:
                                    var: Flow.PhoneAdditional
                                  Email:
                                    var: Flow.Email
                                  SipAddress:
                                    var: Flow.SipAddress
                                  AccountEnabled:
                                    var: Flow.AccountEnabled
                                  LockReason:
                                    var: Flow.LockReason
                                  PasswordExpiresInDays:
                                    var: Flow.PasswordExpiresInDays
                                  Courses:
                                    var: Flow.Courses
                                  PhoneType:
                                    var: Flow.PhoneType
                                  ServiceNowSysId:
                                    var: Flow.ServiceNowSysId
                                  IsValidDeakinObject:
                                    var: Flow.IsValidDeakinObject
                                  IsVIP:
                                    var: Flow.IsVIP
                                  IsExecutive:
                                    var: Flow.IsExecutive
                                  IsTechnicalGroup:
                                    var: Flow.IsTechnicalGroup
                                  HasITAccount:
                                    var: Flow.HasITAccount
                                  TimeOfDay:
                                    var: Flow.TimeOfDay
                                  DayOfWeek:
                                    var: Flow.DayOfWeek
                                  UtcOffsetMinutes:
                                    var: Flow.UTCOffsetMinutes
                                  IsWeekday:
                                    var: Flow.IsWeekday
                                  IvrName:
                                    var: Flow.IvrName
                                  MemberOfGroups:
                                    var: Flow.MemberOfGroups
                                  AccountNotes:
                                    var: Flow.AccountNotes
                                  ExtensionAttribute1:
                                    var: Flow.ExtensionAttribute1
                                  ExtensionAttribute2:
                                    var: Flow.ExtensionAttribute2
                                  ExtensionAttribute3:
                                    var: Flow.ExtensionAttribute3
                                failureOutputs:
                                  errorCode:
                                    noValue: true
                                  status:
                                    noValue: true
                                  correlationId:
                                    noValue: true
                                  entityId:
                                    noValue: true
                                  entityName:
                                    noValue: true
                                  userMessage:
                                    noValue: true
                                  userParamsMessage:
                                    noValue: true
                                  userParams.key:
                                    noValue: true
                                  userParams.value:
                                    noValue: true
                                  details.errorCode:
                                    noValue: true
                                  details.fieldName:
                                    noValue: true
                                  details.entityId:
                                    noValue: true
                                  details.entityName:
                                    noValue: true
                        outputs:
                          failure:
                            actions:
                              - updateData:
                                  name: Update Flow log
                                  statements:
                                    - string:
                                        variable: Flow.FlowLog
                                        value:
                                          exp: Append(Flow.FlowLog,"\nID bridge has failed from -  ",Bot.DeakinIDEntered,".")
                              - callData:
                                  name: Report Bridge Action
                                  timeout:
                                    lit:
                                      minutes: 1
                                  category:
                                    Web Services Data Actions - Deakin:
                                      dataAction:
                                        ReportBridgeActionEvent-v110:
                                          inputs:
                                            Identity:
                                              noValue: true
                                            ConversationId:
                                              exp: Session.ConversationId
                                            Context:
                                              lit: 3 - ID Bridge
                                            Message:
                                              exp: Flow.FlowLog
                                            ResetStatusCounters:
                                              noValue: true
                                          successOutputs:
                                            RequestsProcessed:
                                              noValue: true
                                            SlowRequestsProcessed:
                                              noValue: true
                                            ExceptionCount:
                                              noValue: true
                                            LastException:
                                              noValue: true
                                            BridgeActionEvents:
                                              noValue: true
                                            ProcessStartTime:
                                              noValue: true
                                            DataLastRefreshedAt:
                                              noValue: true
                                            DataLastModified:
                                              noValue: true
                                            DataLastRefreshMessage:
                                              noValue: true
                                            DataLastRefreshTimeTaken:
                                              noValue: true
                                            DataRecordCount:
                                              noValue: true
                                            Version:
                                              noValue: true
                                            PID:
                                              noValue: true
                                          failureOutputs:
                                            errorCode:
                                              noValue: true
                                            status:
                                              noValue: true
                                            correlationId:
                                              noValue: true
                                            entityId:
                                              noValue: true
                                            entityName:
                                              noValue: true
                                            userMessage:
                                              noValue: true
                                            userParamsMessage:
                                              noValue: true
                                            userParams.key:
                                              noValue: true
                                            userParams.value:
                                              noValue: true
                                            details.errorCode:
                                              noValue: true
                                            details.fieldName:
                                              noValue: true
                                            details.entityId:
                                              noValue: true
                                            details.entityName:
                                              noValue: true
                                  outputs:
                                    failure:
                                      actions:
                                        - updateData:
                                            name: Update Flow log
                                            statements:
                                              - string:
                                                  variable: Flow.FlowLog
                                                  value:
                                                    exp: Append(Flow.FlowLog,"\nID bridge failed from ",Bot.DeakinIDEntered,".")
                                    timeout:
                                      actions:
                                        - updateData:
                                            name: Update Flow log
                                            statements:
                                              - string:
                                                  variable: Flow.FlowLog
                                                  value:
                                                    exp: Append(Flow.FlowLog,"\nID bridge timedout from ",Bot.DeakinIDEntered,".")
                          timeout:
                            actions:
                              - updateData:
                                  name: Update Flow log
                                  statements:
                                    - string:
                                        variable: Flow.FlowLog
                                        value:
                                          exp: Append(Flow.FlowLog,"\nID bridge timed out from ",Bot.DeakinIDEntered,".")
                              - callData:
                                  name: Report Bridge Action
                                  timeout:
                                    lit:
                                      minutes: 1
                                  category:
                                    Web Services Data Actions - Deakin:
                                      dataAction:
                                        ReportBridgeActionEvent-v110:
                                          inputs:
                                            Identity:
                                              noValue: true
                                            ConversationId:
                                              exp: Session.ConversationId
                                            Context:
                                              lit: 3 - ID Bridge
                                            Message:
                                              exp: Flow.FlowLog
                                            ResetStatusCounters:
                                              noValue: true
                                          successOutputs:
                                            RequestsProcessed:
                                              noValue: true
                                            SlowRequestsProcessed:
                                              noValue: true
                                            ExceptionCount:
                                              noValue: true
                                            LastException:
                                              noValue: true
                                            BridgeActionEvents:
                                              noValue: true
                                            ProcessStartTime:
                                              noValue: true
                                            DataLastRefreshedAt:
                                              noValue: true
                                            DataLastModified:
                                              noValue: true
                                            DataLastRefreshMessage:
                                              noValue: true
                                            DataLastRefreshTimeTaken:
                                              noValue: true
                                            DataRecordCount:
                                              noValue: true
                                            Version:
                                              noValue: true
                                            PID:
                                              noValue: true
                                          failureOutputs:
                                            errorCode:
                                              noValue: true
                                            status:
                                              noValue: true
                                            correlationId:
                                              noValue: true
                                            entityId:
                                              noValue: true
                                            entityName:
                                              noValue: true
                                            userMessage:
                                              noValue: true
                                            userParamsMessage:
                                              noValue: true
                                            userParams.key:
                                              noValue: true
                                            userParams.value:
                                              noValue: true
                                            details.errorCode:
                                              noValue: true
                                            details.fieldName:
                                              noValue: true
                                            details.entityId:
                                              noValue: true
                                            details.entityName:
                                              noValue: true
                                  outputs:
                                    failure:
                                      actions:
                                        - updateData:
                                            name: Update Flow log
                                            statements:
                                              - string:
                                                  variable: Flow.FlowLog
                                                  value:
                                                    exp: Append(Flow.FlowLog,"\nID bridge failed from ",Bot.DeakinIDEntered,".")
                                    timeout:
                                      actions:
                                        - updateData:
                                            name: Update Flow log
                                            statements:
                                              - string:
                                                  variable: Flow.FlowLog
                                                  value:
                                                    exp: Append(Flow.FlowLog,"\nID bridge timedout from ",Bot.DeakinIDEntered,".")
          - decision:
              name: Did customer enter no for IDNumber?
              condition:
                exp: "Flow.IDNumber==\"no\" \nor Flow.IDNumber==\"n\"\nor Flow.IDNumber==\"NO\""
              outputs:
                "yes":
                  actions:
                    - decision:
                        name: Does MessageNameAndEmail exist in data table?
                        condition:
                          exp: IsNotSetOrEmpty(Flow.MessageNameAndEmail)
                        outputs:
                          "no":
                            actions:
                              - askForSlot:
                                  name: Get Name and Email slot
                                  slot:
                                    name: NameAndEmail
                                  question:
                                    exp: "MakeCommunication(\nFlow.MessageNameAndEmail)"
                                  customValidation:
                                    noValue: true
                                  confirmationMode:
                                    lit: never
                                  noInput:
                                    exp: "MakeCommunication(\n  \"Please say or enter the (please put your slot name here).\")"
                                  noMatch:
                                    exp: "MakeCommunication(\n  \"Please give me the (please put your slot name here) again.\")"
                                  voiceEndOfInputTimeout:
                                    noValue: true
                                  outputs:
                                    maxNoInputs:
                                      enabled: false
                                    maxNoMatches:
                                      enabled: false
                "no":
                  actions:
                    - decision:
                        name: Does MessageNameAndDOB exist in data table?
                        condition:
                          exp: IsNotSetOrEmpty(Flow.MessageNameAndDOB)
                        outputs:
                          "no":
                            actions:
                              - askForSlot:
                                  name: Get Name and DOB slot
                                  slot:
                                    name: NameAndDOB
                                  question:
                                    exp: "MakeCommunication(\nFlow.MessageNameAndDOB)"
                                  customValidation:
                                    noValue: true
                                  confirmationMode:
                                    lit: never
                                  noInput:
                                    exp: "MakeCommunication(\n  \"Please say or enter the (please put your slot name here).\")"
                                  noMatch:
                                    exp: "MakeCommunication(\n  \"Please give me the (please put your slot name here) again.\")"
                                  voiceEndOfInputTimeout:
                                    noValue: true
                                  outputs:
                                    maxNoInputs:
                                      enabled: false
                                    maxNoMatches:
                                      enabled: false
          - decision:
              name: Future use - ask forreason for contacting intent
              condition:
                lit: false
              outputs:
                "yes":
                  actions:
                    - askForIntent:
                        name: Ask for Intent
                        question:
                          exp: "MakeCommunication(\n  ToCommunication(Flow.MessageReasonForCalling))"
                        noInput:
                          exp: "MakeCommunication(\n  \"Just speak a short sentence that describes your query. For example, \\\"Check my account balance\\\"\")"
                        noMatch:
                          exp: "MakeCommunication(\n  \"Please tell me again what you would like to do.\")"
                        voiceEndOfInputTimeout:
                          noValue: true
                        outputs:
                          intents:
                            - intent:
                                name: Defer
                                enabled: true
                            - intent:
                                name: Encumbrance
                                enabled: true
                            - intent:
                                name: Enrol
                                enabled: true
                            - intent:
                                name: Fees
                                enabled: true
                            - intent:
                                name: No intent
                                enabled: true
                          noIntent:
                            enabled: true
                          knowledge:
                            enabled: false
                          maxNoInputs:
                            enabled: false
                "no":
                  actions:
                    - askForSlot:
                        name: Ask for reason for calling
                        slot:
                          name: ReasonForCalling
                        question:
                          exp: "MakeCommunication(\n  MakeCommunication(Flow.MessageReasonForCalling))"
                        customValidation:
                          noValue: true
                        confirmationMode:
                          lit: never
                        noInput:
                          exp: "MakeCommunication(\n  MakeCommunication(Flow.MessageReasonForCalling))"
                        noMatch:
                          exp: "MakeCommunication(\n  \"Please give me the\", \n  \"(please put your slot name here)\")"
                        voiceEndOfInputTimeout:
                          noValue: true
                        outputs:
                          maxNoInputs:
                            enabled: false
                          maxNoMatches:
                            enabled: false
          - exitBotFlow:
              name: Exit Bot Flow
  settingsNaturalLanguageUnderstanding:
    nluDomainVersion:
      intents:
        - utterances:
            - segments:
                - text: defer my studies
              id: 6c75054d-590e-4b20-944d-9821a26165b8
              source: User
            - segments:
                - text: defer
              id: 84a14e2c-b8e3-4786-9ee5-f3fe19cf7917
              source: User
            - segments:
                - text: I need to defer
              id: 0b50548d-a60f-41d9-882b-10c38dbe91cd
              source: User
            - segments:
                - text: I need help deferring
              id: eeef95af-add4-4a9f-aeac-a0b26b57314a
              source: User
            - segments:
                - text: I want to defer
              id: 7cc88ae6-948e-49d6-860d-4ff5c1c12699
              source: User
          entityNameReferences: []
          id: f9f75be5-931e-4e04-bb79-eb7a337a984d
          name: Defer
        - utterances:
            - segments:
                - text: I am not sure why I have an encumbrance
              id: 089cf9c7-f36d-4b16-ac63-68c05dc75c45
              source: User
            - segments:
                - text: I am encumbered and need to pay
              id: 1bcb68ed-7ed4-46bd-b669-c5784f7de096
              source: User
            - segments:
                - text: Get my encumbrance paid
              id: de2005c1-713f-4941-b1c0-c9e9dbfeea44
              source: User
            - segments:
                - text: sort my encumbrance
              id: 1c7dc5ca-a0ef-4f8f-ba03-3a4d8286ab3b
              source: User
            - segments:
                - text: I need an encumbrance lifted
              id: 2618ea4b-19e1-4fa6-a9cb-92dc6d057185
              source: User
          entityNameReferences: []
          id: 1b7e9254-49aa-4642-8e66-6aced7597796
          name: Encumbrance
        - utterances:
            - segments:
                - text: Need help to do my enrolment
              id: 0330a278-2d5f-4dba-a36f-7b90ec310a0e
              source: User
            - segments:
                - text: Enrolling isn't working
              id: 63d417a9-88e2-455e-b565-40c34d221014
              source: User
            - segments:
                - text: I can't enrol
              id: acee6eb3-3d13-4c4c-9f66-0631c1f8a533
              source: User
            - segments:
                - text: Need help to enrol
              id: 80dcf4c0-4d88-4547-b17f-78faff877a5b
              source: User
            - segments:
                - text: Help enrolling
              id: 27361fe8-2d5c-4b7e-8baf-1a03f6f5f54c
              source: User
            - segments:
                - text: I want to enrol
              id: 3d7ec01a-3ee1-45b9-923c-85e8fd9219bc
              source: User
          entityNameReferences: []
          id: fd4e82c7-ecbb-4b9c-afef-2d44eda8f78c
          name: Enrol
        - utterances:
            - segments:
                - text: I need to pay my fees
              id: 3d7bd559-9a6b-4201-acd7-cbd3dea655c7
              source: User
            - segments:
                - text: get my fees sorted
              id: a73049f5-3725-4861-bdaf-f3b9fc618d5d
              source: User
            - segments:
                - text: give fees to deakin
              id: 94a4dd7f-bf11-4c28-afe5-a9d7477f38c3
              source: User
            - segments:
                - text: transfer fees
              id: 4adb10f5-4efe-4f91-b3b1-46cd7523bfd4
              source: User
            - segments:
                - text: pay fees
              id: 640ddaf8-9f30-4b53-8c52-f5f304f0a912
              source: User
            - segments:
                - text: Need to pay fees
              id: c70e1a70-b900-4bae-8806-c545847d8b34
              source: User
            - segments:
                - text: I want to pay my fees
              id: 81099098-c369-401a-bf2f-c47580043830
              source: User
          entityNameReferences: []
          id: d05f34bc-3425-4232-88e7-92b84211ce99
          name: Fees
        - utterances:
            - segments:
                - text: I need technical help
              id: 97c13d18-ba31-40c1-8f1d-8703f53ab68c
              source: User
            - segments:
                - text: I need help with something else
              id: da9826a9-c26a-478e-b07a-a4e204815291
              source: User
          entityNameReferences: []
          id: 1a86cde6-48bc-466f-82cd-d8c0f2e58c96
          name: No intent
      entities:
        - name: IDNumber
          type: IDNumberType
        - name: NameAndDOB
          type: "builtin:any"
        - name: NameAndEmail
          type: "builtin:any"
        - name: ReasonForCalling
          type: "builtin:any"
      entityTypes:
        - name: CustomerType
          description: The description of the Entity Type "CustomerType"
          mechanism:
            type: Regex
            restricted: true
            items:
              - value: "^[a-zA-Z0-9]+$"
                synonyms: []
        - name: EmailAddress
          description: The description of the Entity Type "EmailAddress"
          mechanism:
            type: Regex
            restricted: true
            items:
              - value: "^[a-zA-Z0-9]+$"
                synonyms: []
        - name: IDNumberType
          description: The description of the Entity Type "IDNumberType"
          mechanism:
            type: Regex
            restricted: true
            items:
              - value: "^[a-zA-Z0-9]+$"
                synonyms: []
      language: en-au
      languageVersions: {}
    mutedUtterances: []

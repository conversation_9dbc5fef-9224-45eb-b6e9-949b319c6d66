inqueueCall:
  name: DSL - ISSO in-Queue Flow
  description: "(PCC: acg-purecloud-dsl-isso-supervisor)"
  division: Home
  defaultLanguage: en-au
  supportedLanguages:
    en-au:
      defaultLanguageSkill:
        noValue: true
      textToSpeech:
        Genesys TTS:
          voice: Kandyce
  variables:
    - stringVariable:
        name: Flow.AccountEnabled
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.AccountType
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.AreaCalled
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.AudioLoopsCompleted
        initialValue:
          lit: false
    - booleanVariable:
        name: Flow.Calculated_AreCallbackEnabled
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.CallbackRequired
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.Campus
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.CampusSubstring
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.Courses
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.CoursesString
        initialValue:
          lit: ""
    - stringCollectionVariable:
        name: Flow.CurrentCallbackNumbersFromAPI
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.DisplayName
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.Email
        initialValue:
          lit: ""
    - booleanVariable:
        name: Flow.EnableCallbacks
        initialValue:
          noValue: true
    - booleanVariable:
        name: Flow.EnableForcedCallbacks
        initialValue:
          noValue: true
    - integerVariable:
        name: Flow.EndlessLoop
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.FlowLog
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.FlowLog_InQueue
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.HasITAccount
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.Id
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.IdentifiedBy
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.Interval60
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.IsDev
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.IsExecutive
        initialValue:
          lit: ""
    - booleanVariable:
        name: Flow.IsOpen
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.IsTechnicalGroup
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.IsValidDeakinObject
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.IsVIP
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.IvrName
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.LockReason
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.LockReasonString
        initialValue:
          lit: ""
    - integerVariable:
        name: Flow.LoopQueueIndex
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.OverridePriority
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.PasswordExpiresInDays
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.PhoneAdditional
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.PhoneMobile
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.PhoneNumber
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.PhoneType
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.RequestedCallbackNumber
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.RequestedCallbackNumber_Trimmed
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.RoomSubstring
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.ScriptSet
        initialValue:
          noValue: true
    - stringVariable:
        name: Flow.ServiceNowSysId
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.SipAddress
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.Username
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.VNCCampus
        initialValue:
          lit: ""
    - stringVariable:
        name: Flow.VNCRoom
        initialValue:
          lit: ""
  settingsInQueueCall:
    holdMusic:
      lit:
        name: PromptSystem.on_hold_music
  settingsActionDefaults:
    playAudioOnSilence:
      timeout:
        lit:
          seconds: 40
    detectSilence:
      timeout:
        lit:
          seconds: 40
    callData:
      processingPrompt:
        noValue: true
    callBridge:
      processingPrompt:
        noValue: true
    collectInput:
      noEntryTimeout:
        lit:
          seconds: 5
    dialByExtension:
      interDigitTimeout:
        lit:
          seconds: 6
    transferToUser:
      connectTimeout:
        noValue: true
    transferToNumber:
      connectTimeout:
        noValue: true
    transferToGroup:
      connectTimeout:
        noValue: true
    transferToFlowSecure:
      connectTimeout:
        lit:
          seconds: 15
  settingsErrorHandling:
    errorHandling:
      disconnect:
        none: true
    preHandlingAudio:
      tts: We were unable to transfer your call. Please try again later.
  settingsPrompts:
    ensureAudioInPrompts: false
  startUpTaskActions:
    - getParticipantData:
        name: Get Participant Data
        attributes:
          - attribute:
              name:
                lit: Flow.IvrName
              variable: Flow.IvrName
          - attribute:
              name:
                lit: Flow.IsDev
              variable: Flow.IsDev
    - getParticipantData:
        name: Get bridge Action data
        attributes:
          - attribute:
              name:
                lit: Flow.Username
              variable: Flow.Username
          - attribute:
              name:
                lit: Flow.DisplayName
              variable: Flow.DisplayName
          - attribute:
              name:
                lit: Flow.AccountType
              variable: Flow.AccountType
          - attribute:
              name:
                lit: Flow.Campus
              variable: Flow.Campus
          - attribute:
              name:
                lit: Flow.PhoneNumber
              variable: Flow.PhoneNumber
          - attribute:
              name:
                lit: Flow.Id
              variable: Flow.Id
          - attribute:
              name:
                lit: Flow.PhoneMobile
              variable: Flow.PhoneMobile
          - attribute:
              name:
                lit: Flow.PhoneAdditional
              variable: Flow.PhoneAdditional
          - attribute:
              name:
                lit: Flow.Email
              variable: Flow.Email
          - attribute:
              name:
                lit: Flow.SipAddress
              variable: Flow.SipAddress
          - attribute:
              name:
                lit: Flow.AccountEnabled
              variable: Flow.AccountEnabled
          - attribute:
              name:
                lit: Flow.LockReason
              variable: Flow.LockReason
          - attribute:
              name:
                lit: Flow.PasswordExpiresInDays
              variable: Flow.PasswordExpiresInDays
          - attribute:
              name:
                lit: Flow.Courses
              variable: Flow.Courses
          - attribute:
              name:
                lit: Flow.PhoneType
              variable: Flow.PhoneType
          - attribute:
              name:
                lit: Flow.ServiceNowSysId
              variable: Flow.ServiceNowSysId
          - attribute:
              name:
                lit: Flow.IsValidDeakinObject
              variable: Flow.IsValidDeakinObject
          - attribute:
              name:
                lit: Flow.IsVIP
              variable: Flow.IsVIP
          - attribute:
              name:
                lit: Flow.IsExecutive
              variable: Flow.IsExecutive
          - attribute:
              name:
                lit: Flow.IsTechnicalGroup
              variable: Flow.IsTechnicalGroup
          - attribute:
              name:
                lit: Flow.HasITAccount
              variable: Flow.HasITAccount
    - getParticipantData:
        name: Get Other data
        attributes:
          - attribute:
              name:
                lit: Flow.LockReasonString
              variable: Flow.LockReasonString
          - attribute:
              name:
                lit: Flow.CoursesString
              variable: Flow.CoursesString
          - attribute:
              name:
                lit: Flow.AreaCalled
              variable: Flow.AreaCalled
          - attribute:
              name:
                lit: Flow.IdentifiedBy
              variable: Flow.IdentifiedBy
          - attribute:
              name:
                lit: Flow.IvrName
              variable: Flow.IvrName
          - attribute:
              name:
                lit: Task.ForceCallbackPosition
              variable: Task.ForceCallbackPosition
          - attribute:
              name:
                lit: Flow.Interval60
              variable: Flow.Interval60
          - attribute:
              name:
                lit: Flow.ScriptSet
              variable: Flow.ScriptSet
    - updateData:
        name: Update FlowLog_InQueue
        statements:
          - string:
              variable: Flow.FlowLog_InQueue
              value:
                exp: "Append(Flow.FlowLog_InQueue,\"**Start of In-Queue Flow for \", Call.CurrentQueue.name, \"\\n\",\n\"Current environment is : \", If(Flow.IsDev==\"true\",\"Development\",\"Production\"), \"\\n\",\n\"Current Queue is: \", Call.CurrentQueue.name, \" (\",Call.CurrentQueue.id, \")\", \"\\n\",\n\"IvrName is set to: \", Flow.IvrName, \"\\n\"\n)"
    - setParticipantData:
        name: Set FlowLog_InQueue
        attributes:
          - attribute:
              name:
                lit: Flow.FlowLog_InQueue
              value:
                exp: Flow.FlowLog_InQueue
    - decision:
        name: Decision
        condition:
          exp: Flow.IsDev
        outputs:
          yes:
            actions:
              - dataTableLookup:
                  name: Data Table Lookup
                  lookupValue:
                    exp: Call.Ani
                  dataTable:
                    Testing Numbers:
                      failureOutputs:
                        errorType:
                          noValue: true
                        errorMessage:
                          noValue: true
                  outputs:
                    found:
                      actions:
                        - updateData:
                            name: Update Data
                            statements:
                              - string:
                                  variable: Flow.IsTechnicalGroup
                                  value:
                                    lit: "true"
                        - setParticipantData:
                            name: Set Participant Data
                            attributes:
                              - attribute:
                                  name:
                                    lit: Flow.IsTechnicalGroup
                                  value:
                                    exp: Flow.IsTechnicalGroup
    - updateData:
        name: "PCC: Enable Callback - DSL - ISSO"
        statements:
          - boolean:
              variable: Flow.EnableCallbacks
              value:
                lit: true
    - updateData:
        name: "PCC: Enable Forced Callbacks - DSL - ISSO"
        statements:
          - boolean:
              variable: Flow.EnableForcedCallbacks
              value:
                lit: false
    - evaluateScheduleGroup:
        name: DSL - ISSO
        scheduleGroup:
          name: DSL - ISSO
        inServiceSchedules:
          noValue: true
        evaluate:
          now: true
        outputs:
          open:
            actions:
              - updateData:
                  name: Update Data
                  statements:
                    - boolean:
                        variable: Flow.IsOpen
                        value:
                          lit: true
          closed:
            actions:
              - updateData:
                  name: Update Data
                  statements:
                    - boolean:
                        variable: Flow.IsOpen
                        value:
                          lit: false
          holiday:
            actions:
              - updateData:
                  name: Update Data
                  statements:
                    - boolean:
                        variable: Flow.IsOpen
                        value:
                          lit: false
    - setParticipantData:
        name: Set Participant Data
        attributes:
          - attribute:
              name:
                lit: Flow.IvrName
              value:
                exp: Flow.IvrName
          - attribute:
              name:
                lit: Flow.IsOpen
              value:
                exp: Flow.IsOpen
    - decision:
        name: Decision
        condition:
          exp: Flow.IsOpen
        outputs:
          no:
            actions:
              - updateData:
                  name: Update FlowLog_InQueue
                  statements:
                    - string:
                        variable: Flow.FlowLog_InQueue
                        value:
                          exp: "Append(Flow.Flowlog_InQueue,\nCall.CurrentQueue.name, \"is closed, transferring to General Closure flow for audios and disconnection.\",\"\\n\")"
              - setParticipantData:
                  name: Set FlowLog_InQueue
                  attributes:
                    - attribute:
                        name:
                          lit: Flow.FlowLog_InQueue
                        value:
                          exp: Flow.FlowLog_InQueue
              - transferToFlow:
                  name: Transfer to Flow
                  targetFlow:
                    name: 99 - General Closure
                  failureOutputs:
                    errorType:
                      noValue: true
                    errorMessage:
                      noValue: true
                  outputs:
                    failure:
                      actions:
                        - updateData:
                            name: Update Data
                            statements:
                              - string:
                                  variable: Flow.FlowLog_InQueue
                                  value:
                                    exp: Append(Flow.FlowLog_InQueue, "FAILED - Unable to transfer to general closure", "\n")
                        - setParticipantData:
                            name: Set FlowLog_InQueue
                            attributes:
                              - attribute:
                                  name:
                                    lit: Flow.FlowLog_InQueue
                                  value:
                                    exp: Flow.FlowLog_InQueue
                        - disconnect:
                            name: Disconnect
    - evaluateSchedule:
        name: Evaluate Schedule
        evaluationType:
          schedule:
            name: DSL - ISSO Schedule
            evaluationTimeZone: Australia/Sydney
            evaluate:
              now: true
        outputs:
          active:
            actions:
              - updateData:
                  name: CallbackScheduleEnabled
                  statements:
                    - boolean:
                        variable: Task.CallbackScheduleEnabled
                        value:
                          lit: true
          inactive:
            actions:
              - updateData:
                  name: CallbackScheduleEnabled
                  statements:
                    - boolean:
                        variable: Task.CallbackScheduleEnabled
                        value:
                          lit: false
    - updateData:
        name: Update Data
        statements:
          - boolean:
              variable: Flow.Calculated_AreCallbackEnabled
              value:
                exp: Flow.EnableCallbacks and Task.CallbackScheduleEnabled
    - setParticipantData:
        name: Set Participant Data
        attributes:
          - attribute:
              name:
                lit: Flow.EnableCallbacks
              value:
                exp: Flow.EnableCallbacks
          - attribute:
              name:
                lit: Flow.EnableForcedCallbacks
              value:
                exp: Flow.EnableForcedCallbacks
          - attribute:
              name:
                lit: Flow.Calculated_AreCallbackEnabled
              value:
                exp: Flow.Calculated_AreCallbackEnabled
    - decision:
        name: Decision
        condition:
          exp: Flow.EnableForcedCallbacks or Flow.Calculated_AreCallbackEnabled
        outputs:
          yes:
            actions:
              - callData:
                  name: Get queued callback numbers
                  processingPrompt:
                    lit:
                      name: PromptSystem.on_hold_music
                  timeout:
                    lit:
                      minutes: 1
                  category:
                    PureCloud Data Actions:
                      dataAction:
                        Get queued callback numbers for QUEUE_ID-v3:
                          inputs:
                            QUEUE_ID:
                              exp: Call.CurrentQueue.id
                            interval:
                              exp: Flow.Interval60
                          successOutputs:
                            callbackNumbers:
                              var: Flow.CurrentCallbackNumbersFromAPI
                  outputs:
                    success:
                      actions:
                        - updateData:
                            name: Update Data
                            statements:
                              - string:
                                  variable: Flow.FlowLog_InQueue
                                  value:
                                    exp: "Append(Flow.FlowLog_InQueue, \n\"Forced Callback: \", ToString(Flow.EnableForcedCallbacks), \"\\n\",\n\"Enabled Callbacks: \", ToString(Flow.EnableCallbacks), \"\\n\",\n\"Got queued callback numbers successfully\", \"\\n\",\n\"Flow.Calculated_AreCallbackEnabled: \", Tostring(Flow.Calculated_AreCallbackEnabled), \"\\n\", \n\"Queued numbers are: \", ToString(Flow.CurrentCallbackNumbersFromAPI), \"\\n\"\n\n)"
                        - setParticipantData:
                            name: Set Participant Data
                            attributes:
                              - attribute:
                                  name:
                                    lit: Flow.FlowLog_InQueue
                                  value:
                                    exp: Flow.FlowLog_InQueue
                        - loop:
                            name: Loop
                            currentIndex:
                              var: Task.CallbackNumberIndex
                            loopCount:
                              exp: Count(Flow.CurrentCallbackNumbersFromAPI)
                            outputs:
                              loop:
                                actions:
                                  - decision:
                                      name: Decision
                                      condition:
                                        exp: "FindFirst(Flow.CurrentCallbackNumbersFromAPI,Replace(Call.Ani,\"tel:\",\"\"))!=-1"
                                      outputs:
                                        yes:
                                          actions:
                                            - decision:
                                                name: Decision
                                                condition:
                                                  exp: Call.CurrentQueue.name=="eSolutions - Executive"
                                                outputs:
                                                  no:
                                                    actions:
                                                      - updateData:
                                                          name: Update Data
                                                          statements:
                                                            - boolean:
                                                                variable: Task.CallbackExists
                                                                value:
                                                                  exp: "true"
                                            - loopExit:
                                                name: Exit Loop
                        - setParticipantData:
                            name: Set Participant Data
                            attributes:
                              - attribute:
                                  name:
                                    lit: Flow.CallbackNumber
                                  value:
                                    exp: ToString(Flow.CurrentCallbackNumbersFromAPI,",")
                              - attribute:
                                  name:
                                    lit: Flow.CallbackExists
                                  value:
                                    exp: Task.CallbackExists
                    failure:
                      actions:
                        - updateData:
                            name: Call Data Action failure
                            statements:
                              - string:
                                  variable: Flow.FlowLog_InQueue
                                  value:
                                    exp: "Append(Flow.FlowLog_InQueue, \n\"Forced Callback: \", ToString(Flow.EnableForcedCallbacks), \"\\n\",\n\"Enabled Callbacks: \", ToString(Flow.EnableCallbacks), \"\\n\",\n\"Failed to get Queued callback numbers.\", \"\\n\")"
                    timeout:
                      actions:
                        - updateData:
                            name: Call Data Action timed out
                            statements:
                              - string:
                                  variable: Flow.FlowLog_InQueue
                                  value:
                                    exp: "Append(Flow.FlowLog_InQueue, \n\"Forced Callback: \", ToString(Flow.EnableForcedCallbacks), \"\\n\",\n\"Enabled Callbacks: \", ToString(Flow.EnableCallbacks), \"\\n\",\n\"Queued callback numbers retrieval timed out.\", \"\\n\")"
              - setParticipantData:
                  name: Set Participant Data
                  attributes:
                    - attribute:
                        name:
                          lit: Flow.FlowLog_InQueue
                        value:
                          exp: Flow.FlowLog_InQueue
              - decision:
                  name: Decision
                  condition:
                    exp: Task.CallbackExists
                  outputs:
                    yes:
                      actions:
                        - updateData:
                            name: Update FlowLog_InQueue
                            statements:
                              - string:
                                  variable: Flow.FlowLog_InQueue
                                  value:
                                    exp: Append(Flow.FlowLog_InQueue, "Callback already exists, no callbacks for you!", "\n")
                        - setParticipantData:
                            name: Set FlowLog_InQueue
                            attributes:
                              - attribute:
                                  name:
                                    lit: Flow.FlowLog_InQueue
                                  value:
                                    exp: Flow.FlowLog_InQueue
                        - playAudio:
                            name: Play audio advising call will not continue - existing callback in queue
                            audio:
                              exp: AudioPlaybackOptions(Append(ToAudio(Prompt.PreviousCallForcedDisconnect), ToAudioBlank(1000)), false)
                        - disconnect:
                            name: Disconnect
                    no:
                      actions:
                        - updateData:
                            name: Update FlowLog_InQueue
                            statements:
                              - string:
                                  variable: Flow.FlowLog_InQueue
                                  value:
                                    exp: "Append(Flow.FlowLog_InQueue, \"Callback does not exist\", \"\\n\",\n\"Call.Ani is: \", Call.Ani, \"\\n\",\n\"Call.Ani matched isTel schema?: \", ToString(ToPhoneNumber(Call.Ani).isTel),\"\\n\"\n)"
                        - setParticipantData:
                            name: Set Set FlowLog_InQueue
                            attributes:
                              - attribute:
                                  name:
                                    lit: Flow.FlowLog_InQueue
                                  value:
                                    exp: Flow.FlowLog_InQueue
                        - decision:
                            name: Decision
                            condition:
                              exp: Flow.EnableForcedCallbacks
                            outputs:
                              yes:
                                actions:
                                  - loop:
                                      name: Unknown Number for callback
                                      currentIndex:
                                        var: Task.CollectInputIndex
                                      loopCount:
                                        lit: 2
                                      outputs:
                                        loop:
                                          actions:
                                            - decision:
                                                name: Decision
                                                condition:
                                                  exp: ToPhoneNumber(Call.Ani).isTel
                                                outputs:
                                                  yes:
                                                    actions:
                                                      - updateData:
                                                          name: Set as preferred number
                                                          statements:
                                                            - string:
                                                                variable: Flow.RequestedCallbackNumber
                                                                value:
                                                                  exp: "Replace(Call.Ani,\"tel:\",\"\")"
                                                      - updateData:
                                                          name: Update Data
                                                          statements:
                                                            - boolean:
                                                                variable: Flow.CallbackRequired
                                                                value:
                                                                  lit: true
                                                      - loopExit:
                                                          name: Exit Loop
                                                  no:
                                                    actions:
                                                      - collectInput:
                                                          name: Collect Input
                                                          inputData:
                                                            var: Task.PreferredNumber
                                                          digits:
                                                            range:
                                                              terminatingDtmf: "digit_#"
                                                              min: 5
                                                              max: 20
                                                          interDigitTimeout:
                                                            noValue: true
                                                          noEntryTimeout:
                                                            noValue: true
                                                          inputAudio:
                                                            prompt: Prompt.CallbackPreferredContactNumber
                                                          includeTerminatingDtmfInResultData: false
                                                          acceptJustStar:
                                                            lit: false
                                                          acceptJustPound:
                                                            lit: false
                                                          outputs:
                                                            success:
                                                              actions:
                                                                - updateData:
                                                                    name: Set as preferred number
                                                                    statements:
                                                                      - string:
                                                                          variable: Flow.RequestedCallbackNumber
                                                                          value:
                                                                            exp: Task.PreferredNumber
                                                                - loopExit:
                                                                    name: Exit Loop
                                                            failure:
                                                              actions:
                                                                - playAudio:
                                                                    name: Invalid number
                                                                    audio:
                                                                      prompt: Prompt.SorryPhoneNumberEnteredInvalid
                                                          verificationType: individualDigits
                                  - setParticipantData:
                                      name: Set Participant Data
                                      attributes:
                                        - attribute:
                                            name:
                                              lit: Flow.RequestedCallbackNumber
                                            value:
                                              exp: Flow.RequestedCallbackNumber
                                  - updateData:
                                      name: Update Data
                                      statements:
                                        - string:
                                            variable: Flow.FlowLog_InQueue
                                            value:
                                              exp: "Append(Flow.FlowLog_InQueue,\n\"Callbacks marked as required : \", ToString(Flow.CallbackRequired), \"\\n\"\n)"
                              no:
                                actions:
                                  - updateData:
                                      name: Update Data
                                      statements:
                                        - boolean:
                                            variable: Flow.CallbackRequired
                                            value:
                                              lit: false
          no:
            actions:
              - updateData:
                  name: Update Data
                  statements:
                    - boolean:
                        variable: Flow.CallbackRequired
                        value:
                          lit: false
    - setParticipantData:
        name: Set Participant Data
        attributes:
          - attribute:
              name:
                lit: Flow.CallbackRequired
              value:
                exp: Flow.CallbackRequired
          - attribute:
              name:
                lit: Flow.FlowLog_InQueue
              value:
                exp: Flow.FlowLog_InQueue
    - decision:
        name: Decision
        condition:
          exp: Flow.CallbackRequired
        outputs:
          no:
            actions:
              - updateData:
                  name: Log Scriptset
                  statements:
                    - string:
                        variable: Flow.FlowLog_InQueue
                        value:
                          exp: "Append(Flow.FlowLog_InQueue, \"ScriptSet is set to: \", Flow.ScriptSet, \"\\n\",\nIf(Flow.ScriptSet!=\"DSL - ISSO\",\"Starting process to update script for : \"+Call.CurrentQueue.name,\"\"),\n\"\\n\")"
              - setParticipantData:
                  name: Set Participant Data
                  attributes:
                    - attribute:
                        name:
                          lit: Flow.FlowLog_InQueue
                        value:
                          exp: Flow.FlowLog_InQueue
              - decision:
                  name: Flow.PhoneType == "TS" or Flow.PhoneType == "SHRD"
                  condition:
                    exp: Flow.PhoneType == "TS" or Flow.PhoneType == "SHRD"
                  outputs:
                    yes:
                      actions:
                        - updateData:
                            name: Split retrieved location
                            statements:
                              - string:
                                  variable: Task.Location
                                  value:
                                    exp: Flow.DisplayName
                              - integer:
                                  variable: Task.FirstHyphenIndex
                                  value:
                                    exp: FindString(Task.Location,"-",1)
                              - integer:
                                  variable: Task.SecondHyphenIndex
                                  value:
                                    exp: FindString(Task.Location, "-", 2)
                              - integer:
                                  variable: Task.FirstSpaceIndex
                                  value:
                                    exp: FindString(Task.Location, " ", 1)
                              - integer:
                                  variable: Task.EndOfLocationIndex
                                  value:
                                    exp: If(Task.SecondHyphenIndex != -1, Task.SecondHyphenIndex, If(Task.FirstSpaceIndex != -1, Task.FirstSpaceIndex, -1))
                              - integer:
                                  variable: Task.DistanceFromFirstToLastIndex
                                  value:
                                    exp: If(Task.EndOfLocationIndex != -1, (Task.EndOfLocationIndex - (Task.FirstHyphenIndex + 1)) ,0)
                              - string:
                                  variable: Flow.CampusSubstring
                                  value:
                                    exp: If(Task.FirstHyphenIndex == 1, Substring(Task.Location, 0, 1), "")
                              - string:
                                  variable: Flow.RoomSubstring
                                  value:
                                    exp: "If(Task.FirstHyphenIndex == 1, Substring(Task.Location, Task.FirstHyphenIndex + 1, Task.DistanceFromFirstToLastIndex), \"\")\n"
                    no:
                      actions:
                        - updateData:
                            name: Update Data
                            statements:
                              - string:
                                  variable: Flow.CampusSubstring
                                  value:
                                    exp: "\"\""
                              - string:
                                  variable: Flow.RoomSubstring
                                  value:
                                    exp: "\"\""
                              - string:
                                  variable: Flow.VNCCampus
                                  value:
                                    exp: "\"\""
                              - string:
                                  variable: Flow.VNCRoom
                                  value:
                                    exp: "\"\""
              - decision:
                  name: Decision
                  condition:
                    exp: Flow.ScriptSet=="ISSO"
                  outputs:
                    yes:
                      actions:
                        - updateData:
                            name: Update FlowLog_InQueue
                            statements:
                              - string:
                                  variable: Flow.FlowLog_InQueue
                                  value:
                                    exp: "Append(Flow.FlowLog_InQueue, \n\"ScriptSet is correctly set as: \",Flow.ScriptSet, \n\"\\n\")"
                    no:
                      actions:
                        - decision:
                            name: Decision
                            condition:
                              exp: IsSet(ToPhoneNumber(Call.CalledAddressOriginal).e164)
                            outputs:
                              yes:
                                actions:
                                  - updateData:
                                      name: Update FlowLog_InQueue
                                      statements:
                                        - string:
                                            variable: Flow.FlowLog_InQueue
                                            value:
                                              exp: "Append(Flow.FlowLog_InQueue, \n\"ScriptSet is incorrectly set to: \",Flow.ScriptSet, \n\"Starting process to update script for : \",Call.CurrentQueue.name,\n\"\\n\")"
                                  - setScreenPop:
                                      name: Set Screen Pop
                                      screenPopScript:
                                        ISSOScript:
                                          inputs:
                                            Campus:
                                              exp: Flow.Campus
                                            LockReason:
                                              exp: Flow.LockReasonString
                                            accountIsLocked:
                                              exp: Flow.LockReasonString != "No Lock"
                                            SNContactType:
                                              lit: phone
                                            PhoneNumber:
                                              exp: Flow.PhoneNumber
                                            IsValidDeakinObject:
                                              exp: Flow.IsValidDeakinObject
                                            Inv_IsValidDeakinObject:
                                              exp: "!ToBool(Flow.IsValidDeakinObject)"
                                            isStaff:
                                              exp: Flow.AccountType == "STAFF"
                                            CallBackDate:
                                              lit: ""
                                            SNSys_ID:
                                              exp: Flow.ServiceNowSysId
                                            showTickets:
                                              exp: Flow.AccountType == "STUDENT" or Flow.AccountType == "STAFF"
                                            CallAni:
                                              exp: Call.Ani
                                            containsMoreInfo:
                                              lit: false
                                            CustomerDisplayName:
                                              exp: Flow.DisplayName
                                            Username:
                                              exp: Flow.Username
                                            Id:
                                              exp: Flow.Id
                                            PhoneMobile:
                                              exp: Flow.PhoneMobile
                                            Email:
                                              exp: Flow.Email
                                            InScriptBridgeRan:
                                              lit: false
                                            customerFound:
                                              exp: Flow.AccountType == "STAFF" or Flow.AccountType == "STUDENT"
                                            passwordHasExpired:
                                              exp: ToInt(Flow.PasswordExpiresInDays) <= 0
                                            passwordCannotExpire:
                                              exp: ToInt(Flow.PasswordExpiresInDays) >= 32767
                                            Course:
                                              exp: Flow.CoursesString
                                            isStudent:
                                              exp: Flow.AccountType == "STUDENT"
                                            PhoneType:
                                              exp: Flow.PhoneType
                                            accountHasIssue:
                                              exp: Flow.LockReasonString != "No Lock" or ToInt(Flow.PasswordExpiresInDays) <= 0
                                            isCallBack:
                                              lit: false
                                            RequestedCallbackNumber:
                                              exp: Flow.RequestedCallbackNumber
                                            InteractionID:
                                              exp: Interaction.Id
                                            ConversationID:
                                              exp: Call.ConversationId
                                            IdentifiedBy:
                                              exp: "If(Flow.IdentifiedBy == \"phone\", \"Identified by: Phone Number .\", If(Flow.IdentifiedBy == \"id\", \"Identified by: ID Number .\", \"\"))"
                              no:
                                actions:
                                  - updateData:
                                      name: Update FlowLog_InQueue
                                      statements:
                                        - string:
                                            variable: Flow.FlowLog_InQueue
                                            value:
                                              exp: "Append(Flow.FlowLog_InQueue, \n\"No need to set Script, call is internal\",\n\"\\n\")"
    - decision:
        name: Decision
        condition:
          exp: Flow.CallbackRequired
        outputs:
          no:
            actions:
              - loop:
                  name: Play Audios
                  currentIndex:
                    var: Flow.LoopQueueIndex
                  loopCount:
                    exp: "99"
                  outputs:
                    loop:
                      actions:
                        - decision:
                            name: Decision
                            condition:
                              exp: Contains(Flow.FlowLog_InQueue,"***Beginning of audio loop***")
                            outputs:
                              yes:
                                actions:
                                  - updateData:
                                      name: Update Log
                                      statements:
                                        - string:
                                            variable: Flow.FlowLog_InQueue
                                            value:
                                              exp: "Append(Flow.FlowLog_InQueue,\n\"Audio loop Index here is: \",ToString(Flow.LoopQueueIndex), \"\\n\",\n\"Time is: \", ToString(ToDateTime(ToString(AddHours(GetCurrentDateTimeUtc(),10)))), \"\\n\"\n )"
                              no:
                                actions:
                                  - updateData:
                                      name: Update Data
                                      statements:
                                        - string:
                                            variable: Flow.FlowLog_InQueue
                                            value:
                                              exp: "Append(Flow.FlowLog_InQueue,\n\"----------------------------------------------------------------------\", \"\\n\",\n\"***Beginning of audio loop***\", \"\\n\",\n\"Time is: \", ToString(ToDateTime(ToString(AddHours(GetCurrentDateTimeUtc(),10)))), \"\\n\",\n\"Callbacks marked as required :\",ToString(Flow.CallbackRequired), \"\\n\")"
                        - setParticipantData:
                            name: Set Participant Data
                            attributes:
                              - attribute:
                                  name:
                                    lit: Flow.FlowLog_InQueue
                                  value:
                                    exp: Flow.FlowLog_InQueue
                        - switch:
                            name: Play Audios
                            evaluate:
                              firstTrue:
                                default:
                                  actions:
                                    - updateData:
                                        name: Update Data
                                        statements:
                                          - string:
                                              variable: Flow.FlowLog_InQueue
                                              value:
                                                exp: "Append(Flow.FlowLog_InQueue, \n\"Case Default - Audio Default played\"\n)"
                                    - decision:
                                        name: Decision
                                        condition:
                                          exp: Flow.IsDev=="true" and Flow.IsTechnicalGroup=="true"
                                        outputs:
                                          yes:
                                            actions:
                                              - playAudio:
                                                  name: Play Audio
                                                  audio:
                                                    exp: AudioPlaybackOptions(Append(ToAudioTTS("Placeholder for Default Case Audios"), ToAudioBlank(1000)), true)
                                          no:
                                            actions:
                                              - holdMusic:
                                                  name: Hold Music
                                                  prompt:
                                                    exp: Flow.HoldPrompt
                                                  bargeInEnabled:
                                                    lit: true
                                                  playStyle:
                                                    duration:
                                                      lit:
                                                        seconds: 90
                                cases:
                                  - case:
                                      value:
                                        exp: Flow.LoopQueueIndex==0 and !Flow.AudioLoopsCompleted
                                      actions:
                                        - updateData:
                                            name: Update Data
                                            statements:
                                              - string:
                                                  variable: Flow.FlowLog_InQueue
                                                  value:
                                                    exp: "Append(Flow.FlowLog_InQueue, \n\"Case 1 - Audio 1 played, \", \"\\n\")"
                                        - decision:
                                            name: Decision
                                            condition:
                                              exp: Flow.IsDev=="true" and Flow.IsTechnicalGroup=="true"
                                            outputs:
                                              yes:
                                                actions:
                                                  - playAudio:
                                                      name: Play Audio
                                                      audio:
                                                        exp: AudioPlaybackOptions(Append(ToAudioTTS("Placeholder for Case 1 Audios"), ToAudioBlank(1000)), true)
                                              no:
                                                actions:
                                                  - playAudio:
                                                      name: "PCC: Audio1"
                                                      audio:
                                                        prompt: Prompt.HWB_BookOnlineURL
                                                  - holdMusic:
                                                      name: Hold Music
                                                      prompt:
                                                        exp: Flow.HoldPrompt
                                                      bargeInEnabled:
                                                        lit: true
                                                      playStyle:
                                                        duration:
                                                          lit:
                                                            seconds: 30
                                  - case:
                                      value:
                                        exp: Flow.LoopQueueIndex==1 and !Flow.AudioLoopsCompleted
                                      actions:
                                        - updateData:
                                            name: Update Data
                                            statements:
                                              - string:
                                                  variable: Flow.FlowLog_InQueue
                                                  value:
                                                    exp: "Append(Flow.FlowLog_InQueue, \n\"Case 2 - Audio 2 played, \", \"\\n\"\n)"
                                        - decision:
                                            name: Audio2
                                            condition:
                                              exp: Flow.IsDev=="true" and Flow.IsTechnicalGroup=="true"
                                            outputs:
                                              yes:
                                                actions:
                                                  - playAudio:
                                                      name: Play Audio
                                                      audio:
                                                        exp: AudioPlaybackOptions(Append(ToAudioTTS("Placeholder for Case 2 Audios"), ToAudioBlank(1000)), true)
                                              no:
                                                actions:
                                                  - playAudio:
                                                      name: "PCC: Audio2"
                                                      audio:
                                                        prompt: Prompt.DSL_ISSO_StudySupport
                                                  - holdMusic:
                                                      name: Hold Music
                                                      prompt:
                                                        exp: Flow.HoldPrompt
                                                      bargeInEnabled:
                                                        lit: true
                                                      playStyle:
                                                        duration:
                                                          lit:
                                                            seconds: 30
                                  - case:
                                      value:
                                        exp: Flow.LoopQueueIndex==2 and !Flow.AudioLoopsCompleted
                                      actions:
                                        - updateData:
                                            name: Update Data
                                            statements:
                                              - string:
                                                  variable: Flow.FlowLog_InQueue
                                                  value:
                                                    exp: "Append(Flow.FlowLog_InQueue, \n\"Case 3 - Audio 3 played, \", \"\\n\"\n)"
                                        - decision:
                                            name: Audio3
                                            condition:
                                              exp: Flow.IsDev=="true" and Flow.IsTechnicalGroup=="true"
                                            outputs:
                                              yes:
                                                actions:
                                                  - playAudio:
                                                      name: Play Audio
                                                      audio:
                                                        exp: AudioPlaybackOptions(Append(ToAudioTTS("Placeholder for Case 3 Audios"), ToAudioBlank(1000)), true)
                                              no:
                                                actions:
                                                  - playAudio:
                                                      name: "PCC: Audio3"
                                                      audio:
                                                        exp: AudioPlaybackOptions(ToAudioBlank(100), true)
                                                  - holdMusic:
                                                      name: Hold Music
                                                      prompt:
                                                        exp: Flow.HoldPrompt
                                                      bargeInEnabled:
                                                        lit: true
                                                      playStyle:
                                                        duration:
                                                          lit:
                                                            seconds: 30
                                                  - updateData:
                                                      name: Update Data
                                                      statements:
                                                        - string:
                                                            variable: Flow.FlowLog_InQueue
                                                            value:
                                                              exp: "Append(Flow.FlowLog_InQueue, \n\"Hold audio played, \",\n\"Loop Index here is: \",\nToString(Flow.LoopQueueIndex),\n\"\\n\")"
                                  - case:
                                      value:
                                        exp: Flow.LoopQueueIndex==3 and !Flow.AudioLoopsCompleted
                                      actions:
                                        - updateData:
                                            name: Update Data
                                            statements:
                                              - string:
                                                  variable: Flow.FlowLog_InQueue
                                                  value:
                                                    exp: "Append(Flow.FlowLog_InQueue, \"Case 4 - Audio 4 played, \",\n\"\\n\")"
                                        - decision:
                                            name: Audio4
                                            condition:
                                              exp: Flow.IsDev=="true" and Flow.IsTechnicalGroup=="true"
                                            outputs:
                                              yes:
                                                actions:
                                                  - playAudio:
                                                      name: Play Audio
                                                      audio:
                                                        exp: AudioPlaybackOptions(Append(ToAudioTTS("Placeholder for Case 4 Audios"), ToAudioBlank(1000)), true)
                                              no:
                                                actions:
                                                  - playAudio:
                                                      name: "PCC: Audio4"
                                                      audio:
                                                        prompt: Prompt.DSL_ISSO_StudentCentral
                                                  - holdMusic:
                                                      name: Hold Music
                                                      prompt:
                                                        exp: Flow.HoldPrompt
                                                      bargeInEnabled:
                                                        lit: true
                                                      playStyle:
                                                        duration:
                                                          lit:
                                                            seconds: 30
                                        - updateData:
                                            name: Update Data
                                            statements:
                                              - boolean:
                                                  variable: Flow.AudioLoopsCompleted
                                                  value:
                                                    lit: true
                                        - setParticipantData:
                                            name: Set Participant Data
                                            attributes:
                                              - attribute:
                                                  name:
                                                    lit: Flow.AudioLoopsCompleted
                                                  value:
                                                    exp: Flow.AudioLoopsCompleted
                        - setParticipantData:
                            name: Set FlowLog_InQueue
                            attributes:
                              - attribute:
                                  name:
                                    lit: Flow.FlowLog_InQueue
                                  value:
                                    exp: Flow.FlowLog_InQueue
                        - decision:
                            name: Decision
                            condition:
                              exp: Flow.CallbackRequired
                            outputs:
                              yes:
                                actions:
                                  - loopExit:
                                      name: Exit Loop
                        - decision:
                            name: Callbacks Decision
                            condition:
                              exp: "(Flow.EnableCallbacks and !Flow.CallbackRequired) and \n(Flow.LoopQueueIndex==1 or Flow.LoopQueueIndex==3 or Flow.LoopQueueIndex>=4)"
                            outputs:
                              yes:
                                actions:
                                  - updateData:
                                      name: Update FlowLog_InQueue
                                      statements:
                                        - string:
                                            variable: Flow.FlowLog_InQueue
                                            value:
                                              exp: "Append(Flow.FlowLog_InQueue,\"Callbacks marked as required and in 'correct' loop: \", ToString(Flow.CallbackRequired), \"\\n\")"
                                  - setParticipantData:
                                      name: Set FlowLog_InQueue
                                      attributes:
                                        - attribute:
                                            name:
                                              lit: Flow.FlowLog_InQueue
                                            value:
                                              exp: Flow.FlowLog_InQueue
                                  - collectInput:
                                      name: Would you like a callback?
                                      inputData:
                                        var: Task.CallbackRequested
                                      digits:
                                        exact: 1
                                      interDigitTimeout:
                                        noValue: true
                                      noEntryTimeout:
                                        noValue: true
                                      inputAudio:
                                        prompt: Prompt.PSEC_LikeACallback_PEP
                                      acceptJustStar:
                                        lit: false
                                      acceptJustPound:
                                        lit: false
                                      outputs:
                                        success:
                                          actions:
                                            - decision:
                                                name: Decision
                                                condition:
                                                  exp: Task.CallbackRequested=="1"
                                                outputs:
                                                  yes:
                                                    actions:
                                                      - loop:
                                                          name: Loop
                                                          currentIndex:
                                                            var: Task.CallbackNumberValidationIndex
                                                          loopCount:
                                                            lit: 3
                                                          outputs:
                                                            loop:
                                                              actions:
                                                                - updateData:
                                                                    name: Wants a callback
                                                                    statements:
                                                                      - string:
                                                                          variable: Flow.FlowLog_InQueue
                                                                          value:
                                                                            exp: "Append(Flow.FlowLog_InQueue, \"Callback requested\",\"\\n\",\n\"input was :\", Task.CallbackRequested, \"\\n\")"
                                                                - decision:
                                                                    name: Decision
                                                                    condition:
                                                                      exp: ToPhoneNumber(Call.Ani).isTel or !IsNotSetOrEmpty(Flow.RequestedCallbackNumber)
                                                                    outputs:
                                                                      yes:
                                                                        actions:
                                                                          - updateData:
                                                                              name: Set as preferred number
                                                                              statements:
                                                                                - string:
                                                                                    variable: Flow.RequestedCallbackNumber
                                                                                    value:
                                                                                      exp: Call.Ani
                                                                                - boolean:
                                                                                    variable: Flow.CallbackRequired
                                                                                    value:
                                                                                      lit: true
                                                                          - loopExit:
                                                                              name: Exit Loop
                                                                      no:
                                                                        actions:
                                                                          - collectInput:
                                                                              name: Collect Input
                                                                              inputData:
                                                                                var: Task.PreferredNumber
                                                                              digits:
                                                                                range:
                                                                                  terminatingDtmf: "digit_#"
                                                                                  min: 5
                                                                                  max: 20
                                                                              interDigitTimeout:
                                                                                noValue: true
                                                                              noEntryTimeout:
                                                                                noValue: true
                                                                              inputAudio:
                                                                                prompt: Prompt.CallbackPreferredContactNumber
                                                                              includeTerminatingDtmfInResultData: false
                                                                              acceptJustStar:
                                                                                lit: false
                                                                              acceptJustPound:
                                                                                lit: false
                                                                              outputs:
                                                                                success:
                                                                                  actions:
                                                                                    - updateData:
                                                                                        name: Set as preferred number
                                                                                        statements:
                                                                                          - string:
                                                                                              variable: Flow.RequestedCallbackNumber
                                                                                              value:
                                                                                                exp: Task.PreferredNumber
                                                                                    - updateData:
                                                                                        name: Update Data
                                                                                        statements:
                                                                                          - integer:
                                                                                              variable: Task.CallbackNumberValidationIndex
                                                                                              value:
                                                                                                lit: 5
                                                                                          - boolean:
                                                                                              variable: Flow.CallbackRequired
                                                                                              value:
                                                                                                lit: true
                                                                                failure:
                                                                                  actions:
                                                                                    - playAudio:
                                                                                        name: Invalid number
                                                                                        audio:
                                                                                          prompt: Prompt.SorryPhoneNumberEnteredInvalid
                                                                              verificationType: individualDigits
                                                      - updateData:
                                                          name: Update Data
                                                          statements:
                                                            - string:
                                                                variable: Flow.RequestedCallbackNumber_Trimmed
                                                                value:
                                                                  exp: "Replace(Flow.RequestedCallbackNumber,\"tel:\",\"\")"
                                                      - updateData:
                                                          name: Update Data
                                                          statements:
                                                            - string:
                                                                variable: Flow.FlowLog_InQueue
                                                                value:
                                                                  exp: "Append(Flow.FlowLog_InQueue,\n\"\\n\",\n\"----------------------------------------------------------------------\",\n\"\\n\")"
                                                      - setParticipantData:
                                                          name: Set Participant Data
                                                          attributes:
                                                            - attribute:
                                                                name:
                                                                  lit: Flow.FlowLog_InQueue
                                                                value:
                                                                  exp: Flow.FlowLog_InQueue
                                                            - attribute:
                                                                name:
                                                                  lit: Flow.CallbackRequired
                                                                value:
                                                                  exp: Flow.CallbackRequired
                                                            - attribute:
                                                                name:
                                                                  lit: Flow.RequestedCallbackNumber
                                                                value:
                                                                  exp: Flow.RequestedCallbackNumber
                                                            - attribute:
                                                                name:
                                                                  lit: Flow.RequestedCallbackNumber_Trimmed
                                                                value:
                                                                  exp: Flow.RequestedCallbackNumber_Trimmed
                                                      - loopExit:
                                                          name: Exit Loop
                                                  no:
                                                    actions:
                                                      - updateData:
                                                          name: Update FlowLog_InQueue
                                                          statements:
                                                            - string:
                                                                variable: Flow.FlowLog_InQueue
                                                                value:
                                                                  exp: "Append(Flow.FlowLog_InQueue, \"Callback NOT requested\",\"\\n\",\n\"input was :\", \"\\n\",\nTask.CallbackRequested, \"\\n\",\n\"----------------------------------------------------------------------\", \"\\n\")"
                                                      - setParticipantData:
                                                          name: Set FlowLog_InQueue
                                                          attributes:
                                                            - attribute:
                                                                name:
                                                                  lit: Flow.FlowLog_InQueue
                                                                value:
                                                                  exp: Flow.FlowLog_InQueue
                                        failure:
                                          actions:
                                            - updateData:
                                                name: Update Data
                                                statements:
                                                  - string:
                                                      variable: Task.CallbackRequested
                                                      value:
                                                        lit: "0"
                                            - updateData:
                                                name: Update FlowLog_InQueue
                                                statements:
                                                  - string:
                                                      variable: Flow.FlowLog_InQueue
                                                      value:
                                                        exp: "Append(Flow.FlowLog_InQueue,\n\"Callback request input not detected: \", ToString(Flow.CallbackRequired), \"\\n\",\n\"----------------------------------------------------------------------\",\"\\n\")"
                                            - setParticipantData:
                                                name: Set FlowLog_InQueue
                                                attributes:
                                                  - attribute:
                                                      name:
                                                        lit: Flow.FlowLog_InQueue
                                                      value:
                                                        exp: Flow.FlowLog_InQueue
                                      verificationType: none
                              no:
                                actions:
                                  - updateData:
                                      name: Update FlowLog_InQueue
                                      statements:
                                        - string:
                                            variable: Flow.FlowLog_InQueue
                                            value:
                                              exp: "Append(Flow.FlowLog_InQueue,\nToString(Flow.LoopQueueIndex),\" - Callbacks not offered: \", \nToString(Flow.CallbackRequired), \".\", \"\\n\",\n\"----------------------------------------------------------------------\",\"\\n\")"
                                  - setParticipantData:
                                      name: Set FlowLog_InQueue
                                      attributes:
                                        - attribute:
                                            name:
                                              lit: Flow.FlowLog_InQueue
                                            value:
                                              exp: Flow.FlowLog_InQueue
    - loop:
        name: Loop
        currentIndex:
          var: Flow.EndlessLoop
        loopCount:
          lit: 99
        outputs:
          loop:
            actions:
              - updateData:
                  name: Start of endless loop
                  statements:
                    - string:
                        variable: Flow.FlowLog_InQueue
                        value:
                          exp: "If(\nContains(Flow.FlowLog_InQueue,\"Start of endless loop\"),\nAppend(Flow.FlowLog_InQueue,\"\"),\nAppend(Flow.FlowLog_InQueue, \"Start of endless loop\",\"\\n\")\n   )"
              - updateData:
                  name: Update Data
                  statements:
                    - string:
                        variable: Flow.FlowLog_InQueue
                        value:
                          exp: "Append(Flow.FlowLog_InQueue,\n\"Endless loop index is \", ToString(Flow.EndlessLoop), \"\\n\",\n\"Requested callback number is tel :\", ToString((ToPhoneNumber(Flow.RequestedCallbackNumber).isTel)), \"\\n\",\n\"Trimmed Requested callback number is tel :\", ToString((ToPhoneNumber(Flow.RequestedCallbackNumber_Trimmed).isTel)), \"\\n\"\n)\n"
              - setParticipantData:
                  name: Set Participant Data
                  attributes:
                    - attribute:
                        name:
                          lit: Flow.FlowLog_InQueue
                        value:
                          exp: Flow.FlowLog_InQueue
              - decision:
                  name: Decision
                  condition:
                    exp: Flow.CallbackRequired and (ToPhoneNumber(Flow.RequestedCallbackNumber).isTel)
                  outputs:
                    yes:
                      actions:
                        - updateData:
                            name: Update Data
                            statements:
                              - string:
                                  variable: Flow.FlowLog_InQueue
                                  value:
                                    exp: "Append(Flow.FlowLog_InQueue, \n\"Starting process for \",\nIf(Flow.EnableForcedCallbacks,\"Forced\",\"General\"),\n\" Callback creation\", \"\\n\")"
                        - setParticipantData:
                            name: Set Participant Data
                            attributes:
                              - attribute:
                                  name:
                                    lit: Flow.FlowLog_InQueue
                                  value:
                                    exp: Flow.FlowLog_InQueue
                        - decision:
                            name: Decision
                            condition:
                              exp: Flow.EnableForcedCallbacks
                            outputs:
                              yes:
                                actions:
                                  - playAudio:
                                      name: Play Audio
                                      audio:
                                        prompt: Prompt.eSolForcedCallback
                              no:
                                actions:
                                  - playAudio:
                                      name: Receive callback prompt
                                      audio:
                                        exp: AudioPlaybackOptions(Append(ToAudio(Prompt.CallbackNextAgent), ToAudioBlank(100)), true)
                        - transferToVoicemail:
                            name: Transfer to Voicemail
                            destination:
                              queue:
                                callbackNumber:
                                  exp: ToPhoneNumber(Flow.RequestedCallbackNumber)
                                calleeName:
                                  exp: Flow.DisplayName
                                voicemailGreeting:
                                  prompt: PromptSystem.voicemail_greeting
                                voicemailScript:
                                  ISSOScript:
                                    inputs:
                                      Campus:
                                        exp: Flow.Campus
                                      LockReason:
                                        exp: Flow.Campus
                                      accountIsLocked:
                                        exp: Flow.LockReasonString != "No Lock"
                                      SNContactType:
                                        lit: callback
                                      PhoneNumber:
                                        exp: Flow.PhoneNumber
                                      IsValidDeakinObject:
                                        exp: Flow.IsValidDeakinObject
                                      Inv_IsValidDeakinObject:
                                        exp: "!ToBool(Flow.IsValidDeakinObject)"
                                      isStaff:
                                        exp: Flow.AccountType == "STAFF"
                                      CallBackDate:
                                        lit: ""
                                      SNSys_ID:
                                        exp: Flow.ServiceNowSysId
                                      showTickets:
                                        exp: Flow.AccountType == "STUDENT" or Flow.AccountType == "STAFF"
                                      CallAni:
                                        exp: Call.Ani
                                      containsMoreInfo:
                                        lit: false
                                      CustomerDisplayName:
                                        exp: Flow.DisplayName
                                      Username:
                                        exp: Flow.Username
                                      Id:
                                        exp: Flow.Id
                                      PhoneMobile:
                                        exp: Flow.PhoneMobile
                                      Email:
                                        exp: Flow.Email
                                      InScriptBridgeRan:
                                        lit: false
                                      customerFound:
                                        exp: Flow.AccountType == "STAFF" or Flow.AccountType == "STUDENT"
                                      passwordHasExpired:
                                        exp: ToInt(Flow.PasswordExpiresInDays) <= 0
                                      passwordCannotExpire:
                                        exp: ToInt(Flow.PasswordExpiresInDays) >= 32767
                                      Course:
                                        exp: Flow.CoursesString
                                      isStudent:
                                        exp: Flow.AccountType == "STUDENT"
                                      PhoneType:
                                        exp: Flow.PhoneType
                                      accountHasIssue:
                                        exp: Flow.LockReasonString != "No Lock" or ToInt(Flow.PasswordExpiresInDays) <= 0
                                      isCallBack:
                                        lit: false
                                      RequestedCallbackNumber:
                                        exp: Flow.RequestedCallbackNumber
                                      InteractionID:
                                        exp: Interaction.Id
                                      ConversationID:
                                        exp: Call.ConversationId
                                      IdentifiedBy:
                                        exp: "If(Flow.IdentifiedBy == \"phone\", \"Identified by: Phone Number .\", If(Flow.IdentifiedBy == \"id\", \"Identified by: ID Number .\", \"\"))"
                            failureOutputs:
                              errorType:
                                noValue: true
                              errorMessage:
                                noValue: true
                        - createCallback:
                            name: Create Callback
                            callbackNumber:
                              exp: ToPhoneNumber(Flow.RequestedCallbackNumber)
                            calleeName:
                              noValue: true
                            callbackScript:
                              ISSOScript:
                                inputs:
                                  Campus:
                                    exp: Flow.Campus
                                  LockReason:
                                    exp: Flow.LockReasonString
                                  accountIsLocked:
                                    exp: Flow.LockReasonString != "No Lock"
                                  SNContactType:
                                    lit: phone
                                  PhoneNumber:
                                    exp: Flow.PhoneNumber
                                  IsValidDeakinObject:
                                    exp: Flow.IsValidDeakinObject
                                  Inv_IsValidDeakinObject:
                                    exp: "!ToBool(Flow.IsValidDeakinObject)"
                                  isStaff:
                                    exp: Flow.AccountType == "STAFF"
                                  CallBackDate:
                                    lit: ""
                                  SNSys_ID:
                                    exp: Flow.ServiceNowSysId
                                  showTickets:
                                    exp: Flow.PhoneType == "TS" or Flow.PhoneType == "SHRD" or Flow.AccountType == "STUDENT" or Flow.AccountType == "STAFF"
                                  CallAni:
                                    exp: Call.Ani
                                  containsMoreInfo:
                                    lit: false
                                  CustomerDisplayName:
                                    exp: Flow.DisplayName
                                  Username:
                                    exp: Flow.Username
                                  Id:
                                    exp: Flow.Id
                                  PhoneMobile:
                                    exp: Flow.PhoneMobile
                                  Email:
                                    exp: Flow.Email
                                  InScriptBridgeRan:
                                    lit: false
                                  customerFound:
                                    exp: Flow.AccountType == "STUDENT" or Flow.AccountType == "STAFF"
                                  passwordHasExpired:
                                    exp: ToInt(Flow.PasswordExpiresInDays) <= 0
                                  passwordCannotExpire:
                                    exp: ToInt(Flow.PasswordExpiresInDays) >= 32767
                                  Course:
                                    exp: Flow.CoursesString
                                  isStudent:
                                    exp: Flow.AccountType == "STUDENT"
                                  PhoneType:
                                    exp: Flow.PhoneType
                                  accountHasIssue:
                                    exp: ToInt(Flow.PasswordExpiresInDays) <= 0 or Flow.LockReasonString != "No Lock"
                                  isCallBack:
                                    lit: true
                                  RequestedCallbackNumber:
                                    exp: Flow.RequestedCallbackNumber
                                  InteractionID:
                                    exp: Interaction.Id
                                  ConversationID:
                                    exp: Call.ConversationId
                                  IdentifiedBy:
                                    exp: "If(Flow.IdentifiedBy == \"phone\", \"Identified by: Phone Number .\", If(Flow.IdentifiedBy == \"id\", \"Identified by: ID Number .\", \"\"))"
                        - disconnect:
                            name: Disconnect
                    no:
                      actions:
                        - updateData:
                            name: Update Data
                            statements:
                              - string:
                                  variable: Flow.FlowLog_InQueue
                                  value:
                                    exp: "Append(Flow.FlowLog_InQueue, \"Callback creation should be occurring here, but failed.\", \"\\n\",\n\"Callback required: \", ToString(Flow.CallbackRequired), \"\\n\"\n)"
                        - setParticipantData:
                            name: Set FlowLog_InQueue
                            attributes:
                              - attribute:
                                  name:
                                    lit: Flow.FlowLog_InQueue
                                  value:
                                    exp: Flow.FlowLog_InQueue
    - loop:
        name: Loop
        currentIndex:
          var: Task.HoldMusicLoop
        loopCount:
          lit: 99
        outputs:
          loop:
            actions:
              - decision:
                  name: Decision
                  condition:
                    exp: Flow.IsDev=="true" and Flow.IsTechnicalGroup=="true"
                  outputs:
                    yes:
                      actions:
                        - holdMusic:
                            name: Hold Music
                            prompt:
                              exp: Flow.HoldPrompt
                            bargeInEnabled:
                              lit: false
                            playStyle:
                              duration:
                                lit:
                                  seconds: 15
                    no:
                      actions:
                        - holdMusic:
                            name: Hold Music
                            prompt:
                              exp: Flow.HoldPrompt
                            bargeInEnabled:
                              lit: false
                            playStyle:
                              duration:
                                lit:
                                  minutes: 15
